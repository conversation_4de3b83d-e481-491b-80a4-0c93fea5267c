#!/usr/bin/env node

/**
 * =============================================================================
 * FILE: build-zip.js
 * =============================================================================
 *
 * DESKRIPSI:
 *   File ini mengandung fungsi buildZip yang meng-automasi proses pembuatan arsip
 *   (zip) dari hasil kompilasi TypeScript. Fungsi ini dirancang agar dapat dengan mudah
 *   dikonfigurasi melalui parameter yang diterima sebagai sebuah objek konfigurasi.
 *
 * TUJUAN:
 *   Fungsi buildZip bertujuan untuk:
 *     1. Mengkompilasi kode TypeScript dengan menjalankan "npx tsc", men<PERSON><PERSON><PERSON>an output
 *        di folder build (default: "build").
 *     2. Menyalin file tambahan (misalnya, package.json) ke dalam folder build agar
 *        semua yang diperlukan termasuk dalam arsip.
 *     3. Menentukan nama file zip berdasarkan pola:
 *            {fileNamePrefix}._{YYYY}-{MM}-{DD}._{build_number}_.zip
 *        Dimana:
 *            - {fileNamePrefix} adalah nama file yang ditentukan melalui konfigurasi.
 *            - {YYYY}-{MM}-{DD} adalah tanggal pembuatan.
 *            - {build_number} adalah nomor urut build yang diinkrementasi setiap kali 
 *              dibuat dalam hari yang sama, diambil dari file zip yang ada di folder build.
 *     4. Mengarsip (zip) seluruh isi folder build (yang telah berisi hasil kompilasi dan 
 *        file tambahan) menggunakan perintah sistem "zip".
 *     5. Menyalin file zip yang dibuat dari root project ke folder build.
 *     6. Menghapus file zip yang semula dibuat di root sehingga arsip akhir hanya tersimpan 
 *        di dalam folder build.
 *
 * ALUR KERJA:
 *    a. Mulai dengan menyimpan direktori root project.
 *    b. Eksekusi perintah "npx tsc" untuk mengkompilasi project TypeScript ke dalam folder build.
 *    c. Pastikan folder build ada dan lakukan pengecekan build_number berdasarkan arsip yang
 *       sudah ada di folder build.
 *    d. Salin file-file tambahan (misalnya package.json) ke folder build.
 *    e. Pindah ke folder build dan buat arsip zip dari seluruh kontennya (kecuali folder "test"
 *       dan file dengan ekstensi "*.zip") dengan output arsip disimpan di root.
 *    f. Salin file zip dari root kembali ke folder build.
 *    g. Pindah kembali ke root project dan hapus file zip yang telah dibuat di root.
 *
 * KONFIGURASI:
 *   Fungsi buildZip menerima satu parameter konfigurasi (objek) dengan properti:
 *     - fileNamePrefix: (String) Prefix awal untuk nama file zip, misal "chat-engine".
 *     - includeFiles: (Array of String) Daftar file (menggunakan path relatif ke root) yang
 *                     ingin disertakan ke dalam folder build.
 *     - buildFolder:   (String) Lokasi folder build. Secara default "build".
 *
 * PERSYARATAN:
 *   - Harus dijalankan di lingkungan Node.js.
 *   - Perintah "npx tsc" harus berhasil dijalankan dan menghasilkan folder build.
 *   - Perintah "zip" harus tersedia dan dapat diakses di lingkungan (default pada Mac OS).
 *   - Hak akses file dan folder harus mencukupi.
 *
 * CATATAN:
 *   - Dokumentasi ini sangat terperinci untuk memudahkan LLM ataupun pembaca manusia memahami
 *     setiap langkah dan tujuan dari kode ini.
 *   - Semua log yang dicetak ke console bertujuan untuk memberikan informasi status yang jelas
 *     selama eksekusi.
 *
 * =============================================================================
 */

function buildZip(config = {}) {
  // Import modul modul Node.js yang diperlukan: child_process untuk eksekusi perintah shell,
  // fs untuk operasi file system, dan path untuk manipulasi path.
  const { execSync } = require('child_process');
  const fs = require('fs');
  const path = require('path');

  // ---------------------------------------------------------------------------
  // KONFIGURASI AWAL: MENENTUKAN NILAI DEFAULT DAN MENGAMBIL PARAMETER KONFIGURASI
  // ---------------------------------------------------------------------------
  // fileNamePrefix: String yang menjadi awalan nama file zip.
  // includeFiles: Array yang berisi daftar file yang akan disalin ke folder build.
  // buildFolder: Lokasi tempat hasil build dan arsip zip disimpan.
  const fileNamePrefix = config.fileNamePrefix || "chat-engine";
  const includeFiles = config.includeFiles || [];    // Contoh: ["package.json"]
  const buildFolder = config.buildFolder || "build";   // Default: "build"

  // ---------------------------------------------------------------------------
  // STEP 0: MENYIMPAN DIREKTORI ROOT PROJECT
  // ---------------------------------------------------------------------------
  // Simpan direktori dimana perintah ini dijalankan. Ini akan digunakan untuk navigasi path.
  const rootDir = process.cwd();
  console.log("[INFO] Direktori root project:", rootDir);

  // ---------------------------------------------------------------------------
  // STEP 1: MENJALANKAN KOMPILESI TYPESCRIPT
  // ---------------------------------------------------------------------------
  console.log("[INFO] Mulai proses kompilasi TypeScript dengan 'npx tsc'...");
  try {
    // Eksekusi perintah "npx tsc" dengan menggunakan child_process.execSync sehingga output
    // langsung dialirkan ke console (stdio: 'inherit').
    execSync('npx tsc', { stdio: 'inherit' });
    console.log("[INFO] Kompilasi TypeScript selesai.");
  } catch (error) {
    console.error("[ERROR] Terjadi error saat kompilasi TypeScript:", error);
    process.exit(1);
  }

  // ---------------------------------------------------------------------------
  // STEP 2: VALIDASI DAN PERSIAPAN FOLDER BUILD
  // ---------------------------------------------------------------------------
  // Menggabungkan path root dengan folder build, dan memastikan folder build sudah ada.
  const buildDir = path.join(rootDir, buildFolder);
  if (!fs.existsSync(buildDir)) {
    console.error(
      `[ERROR] Folder '${buildFolder}' tidak ditemukan. Pastikan 'npx tsc' berhasil menghasilkan folder '${buildFolder}'.`
    );
    process.exit(1);
  }
  console.log(`[INFO] Ditemukan folder build: ${buildDir}`);

  // ---------------------------------------------------------------------------
  // STEP 2.1: MENYALIN FILE TAMBAHAN KE FOLDER BUILD
  // ---------------------------------------------------------------------------
  // Untuk memastikan file yang dibutuhkan (misalnya package.json) termasuk dalam arsip,
  // kita salin setiap file yang terdapat dalam array includeFiles dari root ke folder build.
  includeFiles.forEach(fileRelPath => {
    // Tentukan path lengkap sumber dan tujuan
    const srcPath = path.join(rootDir, fileRelPath);
    const destPath = path.join(buildDir, path.basename(fileRelPath));
    console.log(`[INFO] Menyalin file '${fileRelPath}' ke folder build...`);
    try {
      fs.copyFileSync(srcPath, destPath);
      console.log(`[INFO] File '${fileRelPath}' berhasil disalin ke ${destPath}`);
    } catch (error) {
      console.error(`[ERROR] Gagal menyalin file '${fileRelPath}' ke folder build:`, error);
      process.exit(1);
    }
  });

  // ---------------------------------------------------------------------------
  // STEP 3: MENENTUKAN NAMA FILE ZIP DENGAN BUILD NUMBER
  // ---------------------------------------------------------------------------
  // Kita ambil tanggal hari ini untuk membuat bagian dari nama file.
  const today = new Date();
  const YYYY = today.getFullYear();
  // Format bulan dan tanggal agar selalu 2 digit, misalnya "01" untuk Januari.
  const MM = String(today.getMonth() + 1).padStart(2, '0');
  const DD = String(today.getDate()).padStart(2, '0');
  const dateString = `${YYYY}-${MM}-${DD}`;

  // Untuk menentukkan build_number, lakukan pembacaan file yang ada pada folder build.
  // File zip dengan nama yang sesuai pola (misal: chat-engine._2025-01-31._1_.zip) akan diperiksa,
  // kemudian build_number di-set lebih besar dari nomor tertinggi yang ada.
  const buildFiles = fs.readdirSync(buildDir);
  // Regular Expression: ^{fileNamePrefix}\._{dateString}\._(\d+)_\.zip$
  const regex = new RegExp(`^${fileNamePrefix}\\._${dateString}\\._(\\d+)_\\.zip$`);
  let buildNumber = 1;
  buildFiles.forEach(file => {
    const match = file.match(regex);
    if (match) {
      const num = parseInt(match[1], 10);
      // Jika ditemukan file dengan nomor yang lebih tinggi, update buildNumber supaya hasilnya
      // akan diinkrementasi.
      if (num >= buildNumber) {
        buildNumber = num + 1;
      }
    }
  });
  // Gabungkan komponen untuk membentuk nama file zip akhir
  const zipFileName = `${fileNamePrefix}._${dateString}._${buildNumber}_.zip`;
  console.log(`[INFO] Nama file zip yang akan dibuat: ${zipFileName}`);

  // ---------------------------------------------------------------------------
  // STEP 4: MEMBUAT ARSIP ZIP DARI ISI FOLDER BUILD
  // ---------------------------------------------------------------------------
  // Pindahkan direktori kerja ke folder build. Hal ini memastikan bahwa perintah zip
  // hanya mengarsip file di dalam folder build saja.
  process.chdir(buildDir);
  console.log(`[INFO] Berhasil masuk ke folder build: ${process.cwd()}`);

  // Karena perintah zip dibuat dari folder build namun file zip akan disimpan di root,
  // kita gunakan path "../{zipFileName}" sebagai output.
  const outputZipPath = path.join('..', zipFileName);
  // Perintah zip: zip -r <outputZipPath> . -x "test/*" "*.zip"
  // Opsi:
  //   - -r: Recursive, mengarsip seluruh isi folder.
  //   - -x: Mengecualikan file atau folder tertentu. Di sini, folder "test" dan file "*.zip" dikecualikan.
  const zipCommand = `zip -r ${outputZipPath} . -x "test/*" "*.zip"`;
  console.log(`[INFO] Mulai proses pembuatan zip dengan perintah:\n       ${zipCommand}`);
  try {
    execSync(zipCommand, { stdio: 'inherit' });
    console.log(`[INFO] File zip berhasil dibuat di root: ${zipFileName}`);
  } catch (error) {
    console.error("[ERROR] Terjadi error saat pembuatan file zip:", error);
    process.exit(1);
  }

  // ---------------------------------------------------------------------------
  // STEP 5: MENYALIN FILE ZIP DARI ROOT KE FOLDER BUILD
  // ---------------------------------------------------------------------------
  // Untuk memastikan file zip tersedia di folder build (sebagai arsip final),
  // kita salin file zip tersebut dari root ke folder build.
  console.log("[INFO] Menyalin file zip dari root ke folder build...");
  try {
    // Tentukan path file zip sumber (di root) dan path tujuan (di build)
    const srcZipPath = path.resolve(rootDir, zipFileName);  
    const destZipPath = path.join(buildDir, zipFileName);
    fs.copyFileSync(srcZipPath, destZipPath);
    console.log(`[INFO] File zip berhasil disalin ke folder build: ${destZipPath}`);
  } catch (error) {
    console.error("[ERROR] Terjadi error saat menyalin file zip ke folder build:", error);
    process.exit(1);
  }

  // ---------------------------------------------------------------------------
  // STEP 6: MENGHAPUS FILE ZIP YANG ADA DI ROOT
  // ---------------------------------------------------------------------------
  // File zip yang telah dibuat awalnya berada di root, namun kita ingin agar file zip
  // hanya tersimpan di folder build. Maka, kita kembali ke direktori root dan menghapusnya.
  console.log("[INFO] Kembali ke direktori root untuk menghapus file zip yang ada di root...");
  process.chdir(rootDir);
  const rootZipPath = path.resolve(rootDir, zipFileName);
  if (fs.existsSync(rootZipPath)) {
    try {
      fs.unlinkSync(rootZipPath);
      console.log(`[INFO] File zip ${zipFileName} berhasil dihapus dari root.`);
    } catch (error) {
      console.error("[ERROR] Terjadi error saat menghapus file zip dari root:", error);
      process.exit(1);
    }
  } else {
    console.warn(`[WARN] File zip ${zipFileName} tidak ditemukan di root.`);
  }

  // ---------------------------------------------------------------------------
  // AKHIR PROSES
  // ---------------------------------------------------------------------------
  console.log("[INFO] Proses build-zip selesai dengan sukses.");
}

// ---------------------------------------------------------------------------
// PANGGILAN FUNGSI: Menggunakan konfigurasi default, bisa disesuaikan sesuai kebutuhan.
// ---------------------------------------------------------------------------
buildZip({
  fileNamePrefix: "ideal-webhook",
  includeFiles: ["package.json"],
  buildFolder: "build"
});

/**
 * =============================================================================
 * DOCUMENTASI DETAIL
 * =============================================================================
 * 
 * Fungsi buildZip di atas dipecah ke dalam beberapa STEP utama:
 * 
 * STEP 0:
 *   - Menyimpan direktori root yang merupakan lokasi awal eksekusi skrip.
 *   - Hal ini penting agar manipulasi path selanjutnya (misalnya navigasi direktori)
 *     dapat dilakukan dengan benar.
 *
 * STEP 1:
 *   - Melakukan kompilasi TypeScript dengan menjalankan "npx tsc".
 *   - Output kompilasi seharusnya menghasilkan folder build, yang selanjutnya akan diarsipkan.
 *   - Jika kompilasi gagal, skrip akan dihentikan dan error ditampilkan.
 *
 * STEP 2:
 *   - Memastikan folder build ada. Jika tidak, artinya kompilasi gagal atau direktori
 *     build tidak dihasilkan, dan proses selanjutnya tidak dapat dilanjutkan.
 * 
 * STEP 2.1:
 *   - Menyalin file tambahan (seperti package.json) ke dalam folder build. Hal ini
 *     memastikan file tersebut termasuk dalam arsip zip.
 *
 * STEP 3:
 *   - Menentukan format nama file zip. Logging mencakup:
 *       a. Mendapatkan tanggal hari ini.
 *       b. Membangun pola nama file yang mencakup dateString dan build_number.
 *       c. Mengecek folder build untuk file zip yang sudah ada pada hari yang sama sehingga
 *          build_number dapat diinkrementasi dengan benar.
 *
 * STEP 4:
 *   - Pindah ke direktori build agar perintah zip hanya mengarsip isi folder build.
 *   - Menjalankan perintah zip dengan opsi recursive (-r) dan pengecualian file/folder tertentu
 *     (-x "test/*" "*.zip").
 *   - File zip yang dihasilkan disimpan di root.
 *
 * STEP 5:
 *   - Menyalin file zip dari root ke folder build sehingga arsip final tersedia di folder build.
 *
 * STEP 6:
 *   - Menghapus file zip yang berada di root sehingga tidak terjadi duplikasi penyimpanan.
 *
 * Setiap langkah disertai dengan logging yang mendetail, sehingga memudahkan debugging jika
 * ada masalah selama eksekusi.
 *
 * =============================================================================
 */ 