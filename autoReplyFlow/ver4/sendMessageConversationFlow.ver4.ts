import { firestore } from "firebase-admin";
import { IConversationFlowMessage } from "../../types/conversationFlow/ver4/conversationFlow.types";
import { IPayloadQiscusPostMessage } from "../../types/services/qiscus/send_message_types";
import moment from "moment";
import { IFirestoreMessageEntity } from "../../types/firestore/message_types";
import { qiscusServiceNew } from "../../services/QiscusServiceNew";
import { myFirestore } from "../../services/google/firebaseAdmin";
import { firestoreService } from "../../services/google/FirestoreService";
import { AxiosError } from "axios";
import generatePriceListQiscusPayloadFromDreamVehicleVer4 from "./generatePriceListQiscusPayloadFromDreamVehicle.ver4";
import generatePriceListQiscusPayloadFromSourceAdIdVer4 from "./generatePriceListQiscusPayloadFromSourceAdId.ver4";
import generateMessageFromInternalTemplateVer4 from "./generateMessageFromInternalTemplateVer4";
import { ProjectDocumentTypes } from "../../types/firestore/projectDocumentTypes";
import metaServices from "../../services/MetaServices";
import afterSendPriceListAdSourceIdVer4 from "./afterSendPriceListAdSourceId.ver4";
import trimitraArtServices from "../../services/trimitraArt/trimitraArt.services";

const sendMessageConversationFlowVer4 = async (params: {
	startAt: string;
	phoneNumber: string;
	chatRoom: firestore.DocumentReference;
	message: IConversationFlowMessage;
	conversationFlowRef: firestore.DocumentReference;
	projectDoc: firestore.DocumentReference;
}) => {
	const getProjectData = await params.projectDoc.get();
	const dataProject = getProjectData.data() as ProjectDocumentTypes;

	const time = moment();
	const timeFirestore = firestore.Timestamp.fromDate(time.toDate());
	let paramsQiscus: IPayloadQiscusPostMessage = {
		recipient_type: "individual",
		to: params.phoneNumber,
		type: "text",
	};
	const messageParams: IFirestoreMessageEntity = {
		message: {
			id: "",
			direction: "OUT",
			timestamp: timeFirestore,
			unixtime: timeFirestore.seconds,
			type: "text",
		},
		statuses: {
			delivered: null,
			read: null,
			sent: timeFirestore,
			failed: null,
			deleted: null,
		},
		origin: {
			display_name: "SYSTEM_REPLY",
			id: null as any,
			reference: null as any,
			system_reply: true,
		},
	};
	let caption = "";

	const { message } = params;

	if (message.type === "text") {
		paramsQiscus.type = "text";
		paramsQiscus.text = {
			body: message.text.body,
		};
		messageParams.message.type = "text";
		messageParams.message.text = paramsQiscus.text;
		caption = message.text.body;
	} else if (message.type === "button") {
		paramsQiscus.type = "interactive";
		paramsQiscus.interactive = {
			type: "button",
			body: {
				text: message.button.text,
			},
			action: {
				buttons: message.button.buttons
					.filter((b) => b.text && b.id)
					.map((b) => {
						return {
							type: "reply",
							reply: {
								id: b.id,
								title: b.text,
							},
						};
					}),
			},
		};
		messageParams.message.type = "interactive";
		messageParams.message.interactive = paramsQiscus.interactive;

		caption = paramsQiscus.interactive.body.text;
	} else if (
		message.type === "priceList" &&
		params.startAt === "leadsCredit"
	) {
		const generateQiscusPayload =
			await generatePriceListQiscusPayloadFromDreamVehicleVer4({
				chatRoomRef: params.chatRoom,
				projectDoc: params.projectDoc,
			});
		paramsQiscus = {
			to: params.phoneNumber,
			recipient_type: "individual",
			...generateQiscusPayload,
		};
		messageParams.message.type = "interactive";
		messageParams.message.interactive = paramsQiscus.interactive;
		caption = paramsQiscus.interactive?.body.text || "";
	} else if (
		message.type === "priceList" &&
		params.startAt === "referralSourceId"
	) {
		const generateQiscusPayload =
			await generatePriceListQiscusPayloadFromSourceAdIdVer4({
				chatRoomRef: params.chatRoom,
				conversationFlowRef: params.conversationFlowRef,
				projectDoc: params.projectDoc,
			});
		paramsQiscus = {
			to: params.phoneNumber,
			recipient_type: "individual",
			...generateQiscusPayload,
		};
		messageParams.message.type = "interactive";
		messageParams.message.interactive = paramsQiscus.interactive;
		caption = paramsQiscus.interactive?.body.text || "";
	} else if (message.type === "internalTemplate") {
		const generateQiscusPayload =
			await generateMessageFromInternalTemplateVer4({
				templateId: message.internalTemplate.templateId,
				projectDoc: params.projectDoc,
			});

		paramsQiscus = {
			to: params.phoneNumber,
			recipient_type: "individual",
			...generateQiscusPayload,
		};

		if (generateQiscusPayload.type === "button") {
			messageParams.message.type = "interactive";
			messageParams.message.interactive = paramsQiscus.interactive;
			caption = paramsQiscus.interactive?.body.text || "";
		} else if (generateQiscusPayload.type === "text") {
			messageParams.message.type = "text";
			messageParams.message.text = paramsQiscus.text;
			caption = message.text.body;
		}
	} else if (message.type === "mediaTrimitraArt") {
		try {
			const media = await trimitraArtServices.getMediaByArtCode(
				message.mediaTrimitraArt.artCode
			);

			const today = moment();
			const expiredDate = moment(media.data.meta_data?.media_expired_date);

			if (expiredDate.isBefore(today)) {
				throw new Error(
					`Media for artCode "${message.mediaTrimitraArt.artCode}" is expired.`
				);
			}

			const type = media.data.art_assets.type;
			switch (type) {
				case "image":
					paramsQiscus.type = "image";
					paramsQiscus.image = {
						id: media.data.meta_data?.media_id,
						caption: message.mediaTrimitraArt.text,
					};
					messageParams.message.type = "image";
					messageParams.message.image = paramsQiscus.image;
					break;
				case "video":
					paramsQiscus.type = "video";
					paramsQiscus.video = {
						id: media.data.meta_data?.media_id,
						caption: message.mediaTrimitraArt.text,
					};
					messageParams.message.type = "video";
					messageParams.message.video = paramsQiscus.video;
					break;
				default:
					paramsQiscus.type = "text";
					paramsQiscus.text = {
						body: message.mediaTrimitraArt.text,
					};
					messageParams.message.type = "text";
					messageParams.message.text = paramsQiscus.text;
					break;
			}
		} catch (e) {
			console.error(
				`[${new Date().toISOString()}] Error retrieving media for artCode "${message.mediaTrimitraArt.artCode}". Detail error:`,
				e
			);
			paramsQiscus.type = "text";
			paramsQiscus.text = {
				body: message.mediaTrimitraArt.text,
			};
		}
	}

	try {
		if (dataProject.provider === "qiscus") {
			const send = await qiscusServiceNew.sendMessageNative({
				...paramsQiscus,
			});
			messageParams.message.id = send.data.messages[0].id;
		} else if (dataProject.provider === "meta") {
			const send = await metaServices.sendMessage({
				payload: {
					...paramsQiscus,
				},
				meta: {
					bearerToken: dataProject.meta?.bearer || "",
					phoneNumberId: dataProject.meta?.phoneNumberId || "",
				},
			});
			messageParams.message.id = send.data.messages[0].id;
		}
	} catch (e) {
		console.log((e as AxiosError).response?.data);
		throw new Error(`Error Mengirim Pesan`);
	}

	const batch = myFirestore.batch();
	const addNewMessage = await firestoreService.addNewMessage(
		messageParams,
		params.chatRoom,
		messageParams.message.id,
		batch
	);

	const recentChat = {
		contact: params.phoneNumber,
		direction: "OUT",
		display_name: "System",
		statuses: {
			failed: null,
			sent: null,
			read: null,
			delivered: null,
			deleted: null,
		},
		text: caption,
		timestamp: timeFirestore,
		type: messageParams.message.type,
		unixtime: timeFirestore.seconds,
	};

	batch.update(params.chatRoom, {
		recent_chat: recentChat,
	});

	batch.update(params.chatRoom, {
		"wait_for_answer.question_id": message.uuid,
		"wait_for_answer.message_ref": addNewMessage,
		"wait_for_answer.asked_at": timeFirestore,
		"wait_for_answer.topic": params.conversationFlowRef.id,
	});

	await batch.commit();

	if (message.type === "priceList" && params.startAt === "referralSourceId") {
		afterSendPriceListAdSourceIdVer4({
			conversationFlowRef: params.conversationFlowRef,
			chatRoomRef: params.chatRoom,
		})
			.then()
			.catch();
	}

	return addNewMessage;
};

export default sendMessageConversationFlowVer4;
