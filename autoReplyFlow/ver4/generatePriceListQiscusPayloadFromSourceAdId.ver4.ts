import {firestore} from "firebase-admin";
import {myFirestore} from "../../services/google/firebaseAdmin";
import formatToRupiah from "../../helper/formatToRupiah";
import {IPayloadQiscusPostMessage} from "../../types/services/qiscus/send_message_types";
import conversationFlowGetDataDeal from "../ver3/conversationFlowGetDataDeal";

const generatePriceListQiscusPayloadFromSourceAdIdVer4 = async (
    params: {
        chatRoomRef: firestore.DocumentReference;
        conversationFlowRef: firestore.DocumentReference;
        projectDoc: firestore.DocumentReference;
    }
): Promise<Omit<IPayloadQiscusPostMessage, "to" | "recipient_type">> => {
    const getConversationFlowMessage = await params.conversationFlowRef
        .get()
    let dealCode = getConversationFlowMessage.get("referralSourceId.dealCode");
    const getDataDealCode = await conversationFlowGetDataDeal(dealCode);
    const downPayments = getDataDealCode.downPayments;
    let text = "";
    for (const downPayment of downPayments) {
        text = `Untuk Penawaran Kredit ${getDataDealCode.modelName} untuk area ${getDataDealCode.cityGroup} adalah sebagai berikut\n\n`;
        text += `Uang muka ${formatToRupiah(downPayment.downPayment)}\n`;
        text += `Diskon Uang muka ${formatToRupiah(getDataDealCode.promoCode.discount)} (kode promo: ${getDataDealCode.dealCode})\n`;
        for (const tenor of downPayment.tenors) {
            text += `Angsuran ${formatToRupiah(tenor.installment)} x ${tenor.tenor}\n`;
        }
        text += "\n";
    }

    text += `Apakah domisili anda berada di ${getDataDealCode.cityGroup}?`;

    return {
        type: "interactive",
        interactive: {
            type: "button",
            body: {
                text: text,
            },
            action: {
                buttons: [
                    {
                        type: "reply",
                        reply: {
                            id: "YA",
                            title: "YA",
                        }
                    },
                    {
                        type: "reply",
                        reply: {
                            id: "BUKAN",
                            title: "BUKAN",
                        }
                    },
                ]
            }
        }
    };
}

export default generatePriceListQiscusPayloadFromSourceAdIdVer4;
