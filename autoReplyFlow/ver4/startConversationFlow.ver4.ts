import {firestore} from "firebase-admin";
import {IConversationFlowFirestoreDoc} from "../../types/conversationFlow/ver4/conversationFlow.types";
import sendMessageConversationFlowVer4 from "./sendMessageConversationFlow.ver4";

const startConversationFlow = async (params: {
    startAt: string;
    phoneNumber: string;
    chatRoom: firestore.DocumentReference;
    referralSource?: {
        sourceId: string;
    };
    projectDoc: firestore.DocumentReference;
}) => {

    const conversationFlowCollection = params.projectDoc
        .collection("conversation_flow");


    let query = conversationFlowCollection
        .where("startAt", "==", params.startAt)
        .where("active", "==", true)

    if (params.referralSource?.sourceId) {
        query = query
            .where("referralSourceId.sourceId", "==", params.referralSource.sourceId)
    }

    const find = await query.get();

    let data!: IConversationFlowFirestoreDoc;
    let ref!: firestore.DocumentReference;

    if (!find.empty) {
        find.forEach(result => {
            data = result.data() as IConversationFlowFirestoreDoc;
            ref = result.ref;
        })
    } else {
        return;
    }

    const message = data.messages[0];

    return sendMessageConversationFlowVer4({
        chatRoom: params.chatRoom,
        message: message,
        startAt: params.startAt,
        phoneNumber: params.phoneNumber,
        conversationFlowRef: ref,
        projectDoc: params.projectDoc,
    });
}

export default startConversationFlow;
