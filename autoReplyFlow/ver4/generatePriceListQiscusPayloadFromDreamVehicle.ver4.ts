import {firestore} from "firebase-admin";
import formatToRupiah from "../../helper/formatToRupiah";
import {myFirestore} from "../../services/google/firebaseAdmin";
import {IPayloadQiscusPostMessage} from "../../types/services/qiscus/send_message_types";
import conversationFlowGetDataDeal from "../ver3/conversationFlowGetDataDeal";

const generatePriceListQiscusPayloadFromDreamVehicleVer4 = async (params: {
    chatRoomRef: firestore.DocumentReference;
    projectDoc: firestore.DocumentReference;
}): Promise<Omit<IPayloadQiscusPostMessage, "to" | "recipient_type">> => {
    const getChatRoom = await params.chatRoomRef.get();
    const chatRoomData: firestore.DocumentReference[] = getChatRoom.get("clients");
    const getClient = await chatRoomData[0].get();
    const dataClientVehicle = getClient.get("dream_vehicle");
    const dataClientCityGroup = getClient.get("profile.area.text");
    const getVariantPricelistConversationFlow = await params.projectDoc
        .collection("conversation_flow")
        .where("dealCode.modelName", "==", dataClientVehicle.model_name.toUpperCase())
        .where("dealCode.cityGroup", "==", dataClientCityGroup || "")
        .get();

    let dealCode = "";
    let text = "";

    getVariantPricelistConversationFlow.forEach(p => {
        dealCode = p.get("referralSourceId.dealCode")
    })

    if(dealCode) {
        try {
            const getDataDealCode = await conversationFlowGetDataDeal(dealCode);
            const downPayments = getDataDealCode.downPayments;

            for (const downPayment of downPayments) {
                text = `Untuk Penawaran Kredit ${getDataDealCode.modelName} untuk area ${getDataDealCode.cityGroup} adalah sebagai berikut\n\n`;
                text += `Uang muka ${formatToRupiah(downPayment.downPayment)}\n`;
                text += `Diskon Uang muka ${formatToRupiah(getDataDealCode.promoCode.discount)} (kode promo: ${getDataDealCode.dealCode})\n`;
                for (const tenor of downPayment.tenors) {
                    text += `Angsuran ${formatToRupiah(tenor.installment)} x ${tenor.tenor}\n`;
                }
                text += "\n";
            }
        } catch (e) {}
    }

    text += "Apakah sudah pernah kredit sebelumnya?";

    return {
        type: "interactive",
        interactive: {
            type: "button",
            body: {
                text: text,
            },
            action: {
                buttons: [
                    {
                        type: "reply",
                        reply: {
                            id: "SUDAH",
                            title: "SUDAH",
                        }
                    },
                    {
                        type: "reply",
                        reply: {
                            id: "BELUM",
                            title: "BELUM",
                        }
                    },
                ]
            }
        }
    };
}

export default generatePriceListQiscusPayloadFromDreamVehicleVer4;
