import {myFirestore} from "../../services/google/firebaseAdmin";
import {IPayloadQiscusPostMessage} from "../../types/services/qiscus/send_message_types";
import {firestore} from "firebase-admin";

const generateMessageFromInternalTemplateVer4 =
    async (params: {
        templateId: string;
        projectDoc: firestore.DocumentReference;
    }): Promise<Omit<IPayloadQiscusPostMessage, "to" | "recipient_type">> => {

        const projectDoc = params.projectDoc;
        const templateCollection = projectDoc
            .collection("templates");

        const getTemplate = await templateCollection.doc(params.templateId).get();
        if (!getTemplate.exists) throw new Error("Template tidak ditemukan");

        const data = getTemplate.data() as any;

        let payloadSendToQiscus: Omit<IPayloadQiscusPostMessage, "to" | "recipient_type"> = {
            type: "text"
        }
        if (data.type === "button") {
            payloadSendToQiscus.type = "interactive";
            const buttons: { id: string; title: string }[] = [];

            if (data.button.button1) {
                buttons.push({
                    id: data.button.button1.toUpperCase(),
                    title: data.button.button1,
                })
            }

            if (data.button.button2) {
                buttons.push({
                    id: data.button.button2.toUpperCase(),
                    title: data.button.button2,
                })
            }

            if (data.button.button3) {
                buttons.push({
                    id: data.button.button3.toUpperCase(),
                    title: data.button.button3,
                })
            }

            payloadSendToQiscus.interactive = {
                type: "button",
                body: {
                    text: data.text,
                },
                action: {
                    buttons: buttons.map(value => {
                        return {
                            type: "reply",
                            reply: {
                                id: value.id,
                                title: value.title,
                            }
                        }
                    })
                }
            }
        } else {
            payloadSendToQiscus.type = "text";
            payloadSendToQiscus.text = {
                body: data.text,
            }
        }

        return payloadSendToQiscus;
    }

export default generateMessageFromInternalTemplateVer4;
