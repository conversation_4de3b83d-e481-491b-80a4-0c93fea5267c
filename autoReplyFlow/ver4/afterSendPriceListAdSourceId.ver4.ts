import {firestore} from "firebase-admin";
import conversationFlowGetDataDeal from "../ver3/conversationFlowGetDataDeal";
import {IPriceListBigquery, IPriceListBigqueryCreditScheme} from "../../types/bigquery/pricelistBigquery.types";
import {v4} from "uuid";
import {bigQuery, priceListTable} from "../../services/google/bigQueryService";

const afterSendPriceListAdSourceIdVer4 = async (
    params: {
        chatRoomRef: firestore.DocumentReference,
        conversationFlowRef: firestore.DocumentReference;
    }
) => {
    const getChatRoom = await params.chatRoomRef.get();
    const chatRoomData: firestore.DocumentReference[] = getChatRoom.get("clients");
    const getClient = await chatRoomData[0].get();
    const getConversationFlowMessage = await params.conversationFlowRef
        .get()
    let dealCode = getConversationFlowMessage.get("referralSourceId.dealCode");
    const getDataDealCode = await conversationFlowGetDataDeal(dealCode);
    const date = new Date();

    const rows: IPriceListBigquery[] = [];

    getDataDealCode.originalResponse.credit.forEach(value => {
        const generateUuid = v4();
        const row: IPriceListBigquery = {
            event: "sendPricelist",
            uuid: generateUuid,
            phone_number: getClient.get("profile.phone_number"),
            name: getClient.get("profile.name"),
            city_group: getDataDealCode.cityGroup,
            vehicle: {
                "model_name": getDataDealCode.modelName,
                "variant_name": null,
                "variant_code": null,
                "variant_color_code": null,
                "variant_color_name": null
            },
            discount_promo: getDataDealCode.promoCode.discount,
            otr: null,
            pricelists: [],
            admin_id: "SYSTEM",
            created_at: bigQuery.timestamp(date),
            credit: null,
        }

        const scheme: IPriceListBigqueryCreditScheme = {
            discount_down_payment: null,
            discount_installment: null,
            discount_tenor: null,
            down_payment: value.dp_amount,
            installment: value.installment_amount,
            tenor: Number(value.tenor[0]),
        }

        row.pricelists?.push(scheme);
        rows.push(row);
    })

    try {
        await priceListTable.insert(rows);
    } catch (e) {
        return;
    }
}

export default afterSendPriceListAdSourceIdVer4;
