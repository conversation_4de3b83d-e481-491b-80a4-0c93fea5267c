import collect from "collect.js";
import {catalogueServices} from "../../services/catalogueServices";

const conversationFlowGetDataDeal = async (dealCode: string) => {
    const data = await catalogueServices.getDealData(dealCode);

    const promoCode = data.promo_codes.length > 0 ? data.promo_codes[0] : "";
    const promoAmount = data.total_promo_discount;

    const cityGroup = data.area[0].toUpperCase();
    const variantCode = data.vehicle.variant_custom[0].variant_code;
    const modelName = data.vehicle.model_name.toUpperCase();
    const collectPriceList = collect(data.credit);
    const downPayments = collectPriceList.groupBy("dp_amount").keys().toArray();
    const tenors = collectPriceList.groupBy("tenor").keys().toArray();

    const _downPayments: any[] = []

    downPayments.forEach(dp => {
        const _dp = {
            downPayment: parseInt(dp as any),
            tenors: [] as any[],
        }
        tenors.forEach(t => {
            const _tenor = {
                tenor: t,
                installment: 0,
            }
            const find = data.credit.find(v => {
                return (v.dp_amount === _dp.downPayment && v.tenor[0] === t);
            })
            if (find) _tenor.installment = parseInt(find.installment_amount as any);
            _dp.tenors.push(_tenor)
        })

        _downPayments.push(_dp);
    })

    const dealData = {
        cityGroup,
        dealCode,
        downPayments: _downPayments,
        modelName: modelName,
        promoCode: {
            code: promoCode,
            discount: promoAmount,
        },
        variantCode: variantCode,
        originalResponse: data,
    }

    return dealData;
}

export default conversationFlowGetDataDeal;
