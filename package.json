{"name": "autotrimitra-ideal", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "npx ts-node-dev -r dotenv/config index.ts --clear", "start": "node -r dotenv/config ./build/index.js", "build:zip": "node build-zip.js"}, "author": "", "license": "ISC", "devDependencies": {"@types/express": "^5.0.3", "@types/mocha": "^10.0.10", "@types/node": "^24.2.1", "@types/uuid": "^10.0.0", "mocha": "^11.7.1", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.9.2"}, "dependencies": {"@google-cloud/bigquery": "^6.0.3", "@google-cloud/storage": "^6.9.0", "apisauce": "^3.2.0", "axios": "^1.11.0", "collect.js": "^4.36.1", "dotenv": "^17.2.1", "express": "^5.1.0", "express-validator": "^7.2.1", "firebase-admin": "^11.4.1", "form-data": "^4.0.4", "mime": "^4.0.7", "moment": "^2.30.1", "uuid": "^11.1.0"}}