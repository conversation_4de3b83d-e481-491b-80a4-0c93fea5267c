import {myFirestore} from "../services/google/firebaseAdmin";
import {v4 as uuidv4} from 'uuid';

export default class WebhookKataAiModel {

    static async getClient(param: { filter: any }) {
        let isClient, docClient, dataClient;
        const clientRef = myFirestore.collection('clients');
        const snapshot = await clientRef
            .where('contacts.whatsapp', '==', param.filter.contact_wa)
            .get();
        if (snapshot.empty) {
            isClient = false;
            docClient = uuidv4();
            dataClient = {};
        } else {
            snapshot.forEach(doc => {
                isClient = true;
                docClient = doc.id;
                dataClient = doc.data();
            });
        }

        return {isClient, docClient, dataClient};
    }

    static async getRoom(param: { filter: any }) {
        let isRoom, docRoom, dataRoom;
        const roomRef = myFirestore.collection('projects').doc(param.filter.project_doc).collection('chat_rooms');
        const snapshot = await roomRef
            .where('group', '==', false)
            .where('contacts', 'array-contains', param.filter.contact_wa)

            // issue https://github.com/firebase/firebase-js-sdk/issues/1169
            // .where('contact', 'array-contains', param.filter.project_doc)
            // .where('contact', 'array-contains', param.filter.contact_wa)

            .get();
        if (snapshot.empty) {
            isRoom = false;
            docRoom = uuidv4();
            dataRoom = {} as any;
        } else {
            snapshot.forEach(doc => {
                isRoom = true;
                docRoom = doc.id;
                dataRoom = doc.data() as any;
            });
        }

        return {isRoom, roomId: docRoom, dataRoom};
    }

    static async getChat(param: { projectId: string, chatId: string, roomId: string }) {
        let isChat, dataChat;
        const roomRef = myFirestore.collection(`projects/${param.projectId}/chat_rooms`).doc(param.roomId);
        const chatRef = roomRef.collection('chats').doc(param.chatId);
        const snapshot = await chatRef.get();
        if (!snapshot.exists) {
            isChat = false;
            dataChat = {} as any;
        } else {
            isChat = true;
            dataChat = snapshot.data() as any;
        }

        return {isChat, dataChat};

    }

}
