# Triforce Inbound Message Handler

This handler processes incoming messages from Triforce and stores them in Firestore.

## Overview

The `triforceInboundMessageHandler` is responsible for:
1. Validating incoming webhook payloads
2. Processing message data based on type
3. Storing messages in Firestore
4. Handling message status updates
5. Processing special context types like checkin

## Middleware Validation

The handler uses express-validator to ensure payload integrity:

- `messageId`: Must be a non-empty UUID
- `phoneNumber`: Must be non-empty
- `message`: Optional object with conditional validations based on type
- `status`: Optional object for status updates

### Message Type Validations

Supported message types include:
- text
- image
- video
- audio
- document
- voice
- location
- sticker
- button
- interactive
- system
- context
- contacts
- referral

Each type has specific validation rules for its properties.

### Context Validation

Messages can include a context object with type-specific validations:
- `message.context`: Optional object
- `message.context.type`: Must be "checkin" when present
- `message.context.checkin`: Optional object when context type is "checkin"
- `message.context.checkin.count`: Optional integer when context type is "checkin"

### Status Validations

When status is provided:
- `status.read`: Must be a valid ISO8601 date

## Request Body Structure

```typescript
interface ReqBody {
  messageId: string;
  phoneNumber: string;
  message?: Omit<IQiscusReceivedMessageBody, 'from' | 'id' | 'timestamp'> & {
    date: Date;
    context?: {
      type: "checkin",
      checkin?: {
        count: number;
      } | null
    } | null
  };
  status?: {
    read: Date;
  }
}
```

## Handler Logic

### Message Processing

When a message is provided:
1. Creates message context based on message type
2. Processes client data using `clientHandlerHelper`
3. Creates/updates chat room using `chatRoomHandlerHelper`
4. Formats message for Firestore storage
5. Stores message using `firestoreService.addNewMessage`
6. Processes checkin context using `triforceCheckinHelper` when applicable

### Status Updates

When only status is provided:
1. Locates existing chat room by phone number
2. Updates message read status with timestamp

### Checkin Context Processing

When a message includes a checkin context:
1. Calls `triforceCheckinHelper` to process the checkin data
2. Updates relevant documents with checkin count information

## Dependencies

- Firebase Admin SDK
- Firestore Service
- Client Handler Helper
- Chat Room Handler Helper
- Triforce Checkin Helper

## Error Handling

- Returns 500 error if project document is not found
- Uses batch operations for data consistency
- Validates all incoming data before processing

## Response

Returns a JSON object indicating success:
```json
{
  "success": true
}
```

## Usage Examples

### Headers
All requests should include the following headers:
```
Content-Type: application/json
```

### Example 1: Sending a Text Message

When sending a message, the `status` object should be omitted:

**Request Body:**
```json
{
  "messageId": "b02e8d32-8126-4fda-bd0f-97abc53120c3",
  "phoneNumber": "6285763705624",
  "message": {
    "date": "2025-08-11T19:00:00Z",
    "type": "text",
    "text": {
      "body": "Halo selamat malam."
    }
  }
}
```

**Curl Command:**
```bash
curl -X POST {{base_url}}/webhook/triforce/inbound \
  -H "Content-Type: application/json" \
  -d '{
  "messageId": "b02e8d32-8126-4fda-bd0f-97abc53120c3",
  "phoneNumber": "6285763705624",
  "message": {
    "date": "2025-08-11T19:00:00Z",
    "type": "text",
    "text": {
      "body": "Halo selamat malam."
    }
  }
}'
```

### Example 2: Sending a Text Message with Checkin Context

**Request Body:**
```json
{
  "messageId": "b02e8d32-8126-4fda-bd0f-97abc53120c3",
  "phoneNumber": "6285763705624",
  "message": {
    "date": "2025-08-11T19:00:00Z",
    "type": "text",
    "text": {
      "body": "Halo selamat malam."
    },
    "context": {
      "type": "checkin",
      "checkin": {
        "count": 5
      }
    }
  }
}
```

**Curl Command:**
```bash
curl -X POST {{base_url}}/webhook/triforce/inbound \
  -H "Content-Type: application/json" \
  -d '{
  "messageId": "b02e8d32-8126-4fda-bd0f-97abc53120c3",
  "phoneNumber": "6285763705624",
  "message": {
    "date": "2025-08-11T19:00:00Z",
    "type": "text",
    "text": {
      "body": "Halo selamat malam."
    },
    "context": {
      "type": "checkin",
      "checkin": {
        "count": 5
      }
    }
  }
}'
```

### Example 3: Updating Message Status

When updating status, the `message` object should be omitted:

**Request Body:**
```json
{
  "messageId": "b02e8d32-8126-4fda-bd0f-97abc53120c3",
  "phoneNumber": "6285763705624",
  "status": {
    "read": "2025-08-11T23:00:00Z"
  }
}
```

**Curl Command:**
```bash
curl -X POST {{base_url}}/webhook/triforce/inbound \
  -H "Content-Type: application/json" \
  -d '{
  "messageId": "b02e8d32-8126-4fda-bd0f-97abc53120c3",
  "phoneNumber": "6285763705624",
  "status": {
    "read": "2025-08-11T23:00:00Z"
  }
}'
```

### Example 4: Sending an Image Message

**Request Body:**
```json
{
  "messageId": "a1b2c3d4-e5f6-7890-g1h2-i3j4k5l6m7n8",
  "phoneNumber": "6285763705624",
  "message": {
    "date": "2025-08-11T19:05:00Z",
    "type": "image",
    "image": {
      "link": "https://example.com/image.jpg",
      "caption": "Beautiful scenery"
    }
  }
}
```

**Curl Command:**
```bash
curl -X POST {{base_url}}/webhook/triforce/inbound \
  -H "Content-Type: application/json" \
  -d '{
  "messageId": "a1b2c3d4-e5f6-7890-g1h2-i3j4k5l6m7n8",
  "phoneNumber": "6285763705624",
  "message": {
    "date": "2025-08-11T19:05:00Z",
    "type": "image",
    "image": {
      "link": "https://example.com/image.jpg",
      "caption": "Beautiful scenery"
    }
  }
}'
```

## Important Notes

1. Either `message` or `status` should be present in a request, but not both.
2. Date formats should follow ISO 8601 standard (e.g., "2025-08-11T19:00:00Z").
3. The `messageId` must be a valid UUID.
4. Phone numbers should include country code without the leading "+" sign.
5. Context with type "checkin" triggers special processing logic.