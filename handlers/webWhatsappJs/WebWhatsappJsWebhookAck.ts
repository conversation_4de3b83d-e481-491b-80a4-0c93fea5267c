import { json, NextFunction, Request, Response } from "express";
import { body, validationResult } from "express-validator";
import moment from "moment";
import { myFirestore } from "../../services/google/firebaseAdmin";
import { Message, MessageAck } from "../../types/webhook/webWhatsappJsOnMessageWebhookTypes";

interface RequestBody  {
    message: Message;
    ack: MessageAck;
}

const WebWhatsappJsWebhookAck = {
    middlewares: [
        json(),
        body("message").notEmpty(),
        body("ack").notEmpty(),
        function(req: Request, res: Response, next: NextFunction) {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({ errors: errors.array() });
            } else {
                next();
            }
        }
    ],
    handler: async function (req: Request<any, any, RequestBody>, res: Response) {
        let { message, ack } = req.body;

        if(!message.fromMe) return res.send({
            success: false,
        })

        const batch = myFirestore.batch();

        const phoneNumberTarget = message._data.to.replace("@c.us", "");
        const time = moment(message.timestamp * 1000);

        const projectRef = myFirestore
            .collection("projects")
            .doc("B6hfkRsnmcXsKUpFYjN8");

        const chatRoomRef = projectRef.collection("chat_rooms").doc(phoneNumberTarget);

        const chat = chatRoomRef.collection("chats").doc(message.id.id);

        let dataToUpdate = {
        } as any;
        switch(ack) {
            case MessageAck.ACK_DEVICE:
                dataToUpdate = {
                    "statuses.delivered": time.toDate()
                }
                break;
            case MessageAck.ACK_READ:
                dataToUpdate = {
                    "statuses.read": time.toDate()
                }
                break;
            case MessageAck.ACK_ERROR:
                dataToUpdate = {
                    "statuses.failed": time.toDate()
                }
                break;
        }

        batch.update(chat, dataToUpdate);

        try {
            await batch.commit()
            res.json({success: true})
        } catch(e: any) {
            res.status(500).json({success: false,  errors: e})
        }

    }
}

export default WebWhatsappJsWebhookAck;
