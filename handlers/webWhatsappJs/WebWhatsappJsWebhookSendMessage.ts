import { Request, Response, json } from "express";
import { firestore } from "firebase-admin";
import moment from "moment";
import { v4 as uuidV4 } from "uuid";
import { myFirestore } from "../../services/google/firebaseAdmin";
import { Message } from "../../types/webhook/webWhatsappJsOnMessageWebhookTypes";

interface RequestBody {
    message: Message;
    targetName: string;
}

const WebWhatsappJsWebhookSendMessage = {
    middlewares: [
        json(),
    ],
    handler: async function (
        req: Request<any, null, RequestBody>,
        res: Response
    ) {
        let { message } = req.body;

        const batch = myFirestore.batch();

        const phoneNumberTarget = message.to.replace("@c.us", "");
        const myPhoneNumber = message.from.replace("@c.us", "");

        const time = moment(message.timestamp * 1000);

        const projectRef = myFirestore
            .collection("projects")
            .doc("B6hfkRsnmcXsKUpFYjN8");
        const clientCollection = myFirestore.collection("clients");
        const getClient = await clientCollection
            .where("contacts.whatsapp", "==", phoneNumberTarget)
            .get();

        let client!: firestore.DocumentReference;

        if (getClient.empty) {
            const clientUuid = uuidV4();
            client = clientCollection.doc(clientUuid);
            batch.create(client, {
                contacts: {
                    whatsapp: phoneNumberTarget,
                },
                created_time: moment.now(),
                doc_project_origin: projectRef,
                profile: {
                    name: req.body.targetName ?? phoneNumberTarget,
                    phone_number: phoneNumberTarget,
                },
            });
        } else {
            getClient.forEach((snapshot) => {
                client = snapshot.ref;
            });
        }

        const chatRoomRef = projectRef
            .collection("chat_rooms")
            .doc(phoneNumberTarget);
        const getChatRoom = await chatRoomRef.get();

        const direction = "OUT";

        let recentChat = {
            contact: phoneNumberTarget,
            direction: direction,
            display_name: myPhoneNumber,
            text: message.body,
            type: "text",
            timestamp: time.toDate(),
            unixtime: message.timestamp,
        };
        if (!getChatRoom.exists) {
            batch.create(chatRoomRef, {
                clients: [client],
                contacts: [phoneNumberTarget],
                doc_department: null,
                headers: {
                    title: req.body.targetName ?? phoneNumberTarget,
                },
                label: null,
                recent_chat: recentChat,
                wait_for_answer: null,
            });
        } else {
            batch.update(chatRoomRef, {
                recent_chat: recentChat,
            });
        }

        const messagesCollection = chatRoomRef.collection("chats");
        const messageRef = messagesCollection.doc(message.id.id);
        let messageDocument = {
            message: {
                direction: direction,
                text: {
                    body: message.body,
                },
                timestamp: time.toDate(),
                type: "text",
                unixtime: message.timestamp,
            },
            origin: {
                display_name: myPhoneNumber,
                id: myPhoneNumber,
                reference: null,
                system_reply: false,
            },
            statuses: {
                deleted: null,
                delivered: null,
                failed: null,
                read: null,
                sent: time.toDate(),
            },
        };

        batch.create(messageRef, messageDocument);

        try {
            await batch.commit();
            res.json({
                success: true,
            });
        } catch (e: any) {
            console.log(e);
            res.status(500).json({
                success: false,
            });
        }
    },
};

export default WebWhatsappJsWebhookSendMessage;
