import { Request, Response, json } from "express";
import { firestore } from "firebase-admin";
import moment from "moment";
import { v4 as uuidV4 } from "uuid";
import { myFirestore } from "../../services/google/firebaseAdmin";
import { Message } from "../../types/webhook/webWhatsappJsOnMessageWebhookTypes";

interface RequestBody {
    message: Message;
}

const WebWhatsappJsWebhook = {
    middlewares: [
        json(),
    ],
    handler: async function (
        req: Request<any, null, RequestBody>,
        res: Response
    ) {
        let { message } = req.body;

        const batch = myFirestore.batch();

        const phoneNumber = message._data.from.replace("@c.us", "");

        const time = moment(message.timestamp * 1000);

        const projectRef = myFirestore
            .collection("projects")
            .doc("B6hfkRsnmcXsKUpFYjN8");
        const clientCollection = myFirestore.collection("clients");
        const getClient = await clientCollection
            .where("contacts.whatsapp", "==", phoneNumber)
            .get();

        let client!: firestore.DocumentReference;

        if (getClient.empty) {
            const clientUuid = uuidV4();
            client = clientCollection.doc(clientUuid);
            batch.create(client, {
                contacts: {
                    whatsapp: phoneNumber,
                },
                created_time: moment.now(),
                doc_project_origin: projectRef,
                profile: {
                    name: message._data.notifyName,
                    phone_number: phoneNumber,
                },
            });
        } else {
            getClient.forEach((snapshot) => {
                client = snapshot.ref;
            });
        }

        const chatRoomRef = projectRef
            .collection("chat_rooms")
            .doc(phoneNumber);
        const getChatRoom = await chatRoomRef.get();

        let recentChat = {
            contact: phoneNumber,
            direction: "IN",
            display_name: message._data.notifyName,
            text: message.body,
            type: "text",
            timestamp: time.toDate(),
            unixtime: message.timestamp,
        };
        if (!getChatRoom.exists) {
            batch.create(chatRoomRef, {
                clients: [client],
                contacts: [phoneNumber],
                doc_department: null,
                headers: {
                    title: message._data.notifyName,
                },
                label: null,
                recent_chat: recentChat,
                wait_for_answer: null,
            });
        } else {
            batch.update(chatRoomRef, {
                recent_chat: recentChat,
            });
        }

        const messagesCollection = chatRoomRef.collection("chats");
        const messageRef = messagesCollection.doc(message.id.id);
        let messageDocument = {
            message: {
                direction: "IN",
                text: {
                    body: message.body,
                },
                timestamp: time.toDate(),
                type: "text",
                unixtime: message.timestamp,
            },
            origin: {
                display_name: message.from,
                id: phoneNumber,
                reference: client,
                system_reply: false,
            },
            statuses: {
                deleted: null,
                delivered: null,
                failed: null,
                read: null,
                sent: null,
            },
        };

        batch.create(messageRef, messageDocument);

        try {
            await batch.commit();
            res.json({
                success: true,
            });
        } catch (e: any) {
            res.status(500).json({
                success: false,
            });
        }
    },
};

export default WebWhatsappJsWebhook;
