import {Request, Response} from "express";
import {myFirestore} from "../services/google/firebaseAdmin";
import { firestore } from "firebase-admin";
import {IAdminSessionLog, IAdminSessionLogAnalyticData} from "../types/firestore/session_log_types";
import {IFirestoreMessageEntity} from "../types/firestore/message_types";
import {adminSessionLogBigquery} from "../services/google/bigQueryService";
import {IAdminTypes} from "../types/firestore/admin-types";
import collect from "collect.js";

export default class AdminLogHandler {
    public static middlewares = [];
    private static helpers = {
        inOutLink: (messages: Pick<IFirestoreMessageEntity, "message" | "origin">[]): { inboundLink: number; outboundLink: number } => {
            let inboundLink = 0, outboundLink = 0;
            for (const [, message] of messages.entries()) {
                let text!: string;

                if (message.message.type === "text") {
                    text = message.message.text?.body ?? "";
                } else {
                    text = message.message.image?.caption ?? "";
                }

                if (new RegExp("([a-zA-Z0-9]+://)?([a-zA-Z0-9_]+:[a-zA-Z0-9_]+@)?([a-zA-Z0-9.-]+\\.[A-Za-z]{2,4})(:[0-9]+)?(/.*)?").test(text)) {
                    if (message.message.direction === "IN") inboundLink++;
                    else outboundLink++;
                }
            }

            return {
                inboundLink: inboundLink,
                outboundLink: outboundLink,
            }
        }
    }

    public static async handler(req: Request, res: Response) {
        const collection = myFirestore.collection('admin_logs');
        const getDocs = await collection
            .where('auto_end_session_at', '<', firestore.Timestamp.now())
            .where('sent_to_bigquery_at', '==', null)
            .get();

        const docs: firestore.DocumentSnapshot<firestore.DocumentData>[] = []

        getDocs.forEach(result => {
            docs.push(result);
        })

        const dataAnalytics: IAdminSessionLogAnalyticData[] = [];

        const sessionDocChunk = collect(docs).chunk(450);
        const batches: firestore.WriteBatch[] = [];

        for (const chunkPartSessionDoc of sessionDocChunk.all()) {
            for (const sessionDoc of chunkPartSessionDoc) {
                const batch = myFirestore.batch();
                batches.push(batch);
                try {
                    const data = sessionDoc.data() as IAdminSessionLog;

                    const analyticData: IAdminSessionLogAnalyticData = {} as any;

                    let messages = data.messages;
                    const {outboundLink} = AdminLogHandler.helpers.inOutLink(messages);
                    let outboundMessages = messages.filter(value => value.message.direction === "OUT")

                    analyticData.uuid = sessionDoc.ref.id;
                    analyticData.agent_uid = data.admin.ref.id;
                    analyticData.agent_email = data.admin.email || data.admin.name;
                    analyticData.agent_name = data.admin.name;
                    analyticData.session_start = data.signed_in_at.toDate();
                    analyticData.session_end = data.last_heartbeat.toDate();
                    analyticData.session_duration = (analyticData.session_end.getTime() / 1000) - (analyticData.session_start.getTime() / 1000)
                    analyticData.session_duration = Math.ceil(analyticData.session_duration);
                    analyticData.outbound_text = outboundMessages
                        .filter(value => value.message.type === "text" || (value.message.type === "image" && value.message.image?.caption)).length;
                    analyticData.outbound_img = outboundMessages.filter(value => value.message.type === "image").length;
                    analyticData.outbound_link = outboundLink;
                    analyticData.created_at = new Date();
                    analyticData.messages = [];

                    batch.update(sessionDoc.ref, {
                        analytic_data: analyticData,
                        sent_to_bigquery_at: new Date(),
                    })

                    dataAnalytics.push(analyticData);

                } catch (e: any) {
                    console.log(sessionDoc.ref.id, e);
                    batch.update(sessionDoc.ref, {
                        error: {
                            error: true,
                            reason: e,
                        }
                    })
                }
            }
        }

        try {
            if (dataAnalytics.length >= 1) {
                await adminSessionLogBigquery.insert(dataAnalytics);
                for (const batch of batches) {
                    await batch.commit();
                }
            }
            res.send({
                success: true,
            })
        } catch (e: any) {
            console.log(JSON.stringify(e))
            res.status(500).send({
                success: false,
                message: e.toString(),
            })
        }


    }
}
