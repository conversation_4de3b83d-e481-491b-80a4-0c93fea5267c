import {json, NextFunction, Request, Response} from "express";
import {myFirestore} from "../../services/google/firebaseAdmin";
import {
  IBaseQiscusReceivedMessage,
  INewBaseQiscusReceivedMessage,
} from "../../types/services/qiscus/received_message_types";
import {IBaseQiscusReceivedNotification} from "../../types/services/qiscus/notification_message_types";
import {isInboundMessage} from "../../types/webhook/all_new_webhook_types";

import {clientHandlerHelper} from "../../helper/webhook/client";
import {ISessionMessageRef, TSessionOfUser,} from "../../types/webhook/qiscuss";
import {chatRoomHandlerHelper} from "../../helper/webhook/chatRoom";
import moment from "moment";
import {sessionHandlerHelper} from "../../helper/webhook/sessionContext";
import {messageContextHandlerHelper} from "../../helper/webhook/messageContext";

import {isStatuses} from "../../types/webhook/webhook_message_statuses_types";
import {firestore} from "firebase-admin";
import {IFirestoreMessageEntity} from "../../types/firestore/message_types";
import {ProjectDocumentTypes} from "../../types/firestore/projectDocumentTypes";
import sendMessageSlowResponse from "../../helper/webhook/sendMessageSlowResponse";
import startConversationFlowVer4 from "../../autoReplyFlow/ver4/startConversationFlow.ver4";
import flowOnWebhookVer4 from "../../autoReplyFlow/ver4/flowOnWebhook.ver4";
import logAdSourceId from "../../helper/webhook/logAdSourceId";
import setClientCreditSchemeFromAdSourceId from "../../helper/webhook/setClientCreditSchemeFromAdSourceId";
import createLeadsFromInboundMessage from "../../services/ideal_backend/createLeadsFromInboundMessage";
import idealServices from "../../services/ideal_backend/idealServices";
import llmGenerateMessage from "../../helper/webhook/llmGenerateMessage";
import sendMessageWithAi from "../../helper/webhook/sendMessageWithAi";

function parser(params: INewBaseQiscusReceivedMessage) {
  return params.entry[0].changes[0].value.whatsapp_business_api_data;
}

class QiscusWebhookHandler {
  public static middlewares = [
    json(),
    async (
      req: Request<
        {},
        any,
        IBaseQiscusReceivedNotification | IBaseQiscusReceivedMessage
      >,
      res: Response,
      next: NextFunction
    ) => {
      try {
        console.log(
          "QISCUS_WEBHOOK_NOTIFICATION",
          JSON.stringify(req.body)
        );
        if (isInboundMessage(req.body)) {
          const phoneNumber = req.body.messages[0].from;
          const id = req.body.messages[0].id;
          const docPath = myFirestore
            .collection("whatsapp_webhook_log")
            .doc(phoneNumber)
            .collection("qiscus")
            .doc(id);
          await docPath.set({
            ...req.body,
            type: "message",
          });
        } else if (isStatuses(req.body)) {
          const phoneNumber = req.body.statuses[0].recipient_id;
          const id = req.body.statuses[0].id;
          const docPath = myFirestore
            .collection("whatsapp_webhook_log")
            .doc(phoneNumber)
            .collection("qiscus")
            .doc(id);
          await docPath.set({
            ...req.body,
            type: "statuses",
          });
        }
      } catch (e) {
      }
      next();
    },
  ];

  public static async handlerInboundMessage(
    req: Request<
      any,
      any,
      IBaseQiscusReceivedMessage | IBaseQiscusReceivedNotification
    >,
    res: Response,
    next: NextFunction
  ) {
    const sessionType: ISessionMessageRef = {
      sessionType: TSessionOfUser.EXTEND_SESSION,
    };
    const projectRef = myFirestore
      .collection("projects")
      .doc("v8GGopG1v89nd46aajEX");

    const projectDoc = await projectRef.get();
    if (!projectDoc.exists) {
      return res.status(500).send({
        success: false,
        message: "Project tidak ditemukan",
      });
    }
    const projectData = projectDoc.data() as ProjectDocumentTypes;

    let clientRef!: firestore.DocumentReference;
    let chatRoomRef!: firestore.DocumentReference;

    if (isInboundMessage(req.body)) {
      try {
        const messageBody = req.body.messages[0];
        const phoneNumber =
          req.body.contacts?.[0].wa_id ?? req.body.messages[0].from;
        const messageDate = moment
          .unix(parseInt(messageBody.timestamp))
          .toDate();

        let name = req.body.contacts?.[0].profile.name ?? phoneNumber;

        if (messageBody.text?.body) {
          await createLeadsFromInboundMessage({
            messageContent: messageBody.text.body,
            phoneNumber: phoneNumber,
            name: name,
          }).catch((e) => {
            console.error(
              `[${new Date().toISOString()}] Error creating leads from inbound message. Detail error:`,
              e
            );
          });
        }

        let firestoreBatch = myFirestore.batch();

        const clientHandler = await clientHandlerHelper(
          {
            phoneNumber: phoneNumber,
            batch: firestoreBatch,
            name: name,
            messageContext: messageBody,
            organization: "amartahonda",
            organization_group: "amartamotor",
          },
          sessionType
        );

        clientRef = clientHandler.clientRef;

        const chatRoomHandler = await chatRoomHandlerHelper({
          projectRef: projectRef,
          batch: firestoreBatch,

          name: clientHandler.name || phoneNumber,
          phoneNumber: phoneNumber,

          date: messageDate,

          clientRef: clientRef,

          messageContext: messageBody,

          cityGroup: !!clientHandler.city
            ? clientHandler.city
            : undefined,
          organization: "amartahonda",
          organization_group: "amartamotor",
          agentAiReplyNewConversation: Boolean(projectData.agent_ai_reply),
        });

        chatRoomRef = chatRoomHandler.chatRoomRef;

        const sessionHandler = await sessionHandlerHelper(
          {
            roomRef: chatRoomRef,
            messageId: messageBody.id,
            name: name,
            phoneNumber: phoneNumber,
            clientRef: clientRef,
            batch: firestoreBatch,
            date: messageDate,
            messageContext: messageBody,
          },
          sessionType
        );

        await messageContextHandlerHelper({
          roomRef: chatRoomRef,
          sessionId: sessionHandler.sessionId,
          messageId: messageBody.id,
          name: name,
          phoneNumber: phoneNumber,
          clientRef: clientRef,
          batch: firestoreBatch,
          date: messageDate,
          messageContext: messageBody,
        });

        await firestoreBatch.commit();

        if (messageBody.context?.id) {
          const getMessageContextId = await chatRoomRef
            .collection("chats")
            .doc(messageBody.context.id)
            .get();
          const messageContextData =
            getMessageContextId.data() as IFirestoreMessageEntity;
          if (messageContextData.bindContextAndDocuments) {
            for (const bindDocument of messageContextData.bindContextAndDocuments) {
              const docRef =
                bindDocument.path as firestore.DocumentReference;
              if (bindDocument.context === "add_raw_leads") {
                const responseText =
                  messageBody.button?.text ??
                  "MESSAGE_FAILED_TO_FETCH";

                idealServices.notifyRawLeadsReply({
                  path: docRef.path,
                  messageId: messageBody.context?.id ?? "",
                  createdAt: messageDate,
                  text: responseText,
                }).then()
                  .catch()

                if (responseText === "Pembelian Kredit") {
                  startConversationFlowVer4({
                    phoneNumber: phoneNumber,
                    startAt: "leadsCredit",
                    chatRoom: chatRoomRef,
                    projectDoc: projectRef,
                  });
                }
              }
            }
          }
        }



        // await sendMessageSlowResponse({
        //   phoneNumber: phoneNumber,
        //   chatRoomRef: chatRoomRef,
        //   clientRef: clientRef,
        //   session: sessionType,
        //   projectRef: projectRef,
        // }).catch(() => {
        // });
        //
        // try {
        //   if (messageBody.referral) {
        //     await setClientCreditSchemeFromAdSourceId({
        //       clientRef: clientRef,
        //       adSourceId: messageBody.referral.source_id,
        //       projectRef: projectRef,
        //     });
        //     await logAdSourceId({
        //       sourceId: messageBody.referral.source_id,
        //       phoneNumber: phoneNumber,
        //       sessionType: sessionType.sessionType,
        //       projectRef: projectRef,
        //     });
        //     await startConversationFlowVer4({
        //       startAt: "referralSourceId",
        //       chatRoom: chatRoomRef,
        //       phoneNumber: phoneNumber,
        //       referralSource: {
        //         sourceId: messageBody.referral.source_id,
        //       },
        //       projectDoc: projectRef,
        //     });
        //   } else if (clientHandler.isNewUser) {
        //     await startConversationFlowVer4({
        //       startAt: "newCustomer",
        //       chatRoom: chatRoomRef,
        //       phoneNumber: phoneNumber,
        //       projectDoc: projectRef,
        //     });
        //   } else {
        //     await flowOnWebhookVer4({
        //       chatRoom: chatRoomRef,
        //       phoneNumber: phoneNumber,
        //       message: messageBody,
        //       projectDoc: projectRef,
        //     });
        //   }
        // } catch (e) {
        //   console.log(e);
        // }

        await sendMessageWithAi({
          chatRoomRef: chatRoomRef,
          clientRef: clientRef,
          phoneNumber: phoneNumber,
          projectRef: projectRef,
          replyMessageId: messageBody.id,
        })

        return res.send({
          success: true,
        });
      } catch (e: any) {
        console.log(e);
        return res.status(500).send({
          success: false,
          data: JSON.stringify(e),
        });
      }
    } else {
      next();
    }
  }

  public static async handlerMessageNotification(
    req: Request<any, any, IBaseQiscusReceivedNotification>,
    res: Response
  ) {
    const projectRef = myFirestore
      .collection("projects")
      .doc("v8GGopG1v89nd46aajEX");
    let status = req.body.statuses[0].status;

    const phoneNumber = req.body.statuses[0].recipient_id;
    const messageId = req.body.statuses[0].id;

    new Promise(async (resolve, reject) => {
      try {
        const collection = myFirestore.collection(
          "phone_number_crm_template"
        );
        const doc = collection.doc(messageId);

        await doc.set(
          {
            [status]: firestore.Timestamp.fromMillis(
              parseInt(req.body.statuses[0].timestamp) * 1000
            ),
          },
          {
            merge: true,
          }
        );
        resolve(true);
      } catch (e) {
        reject(e);
      }
    })
      .then(() => {
      })
      .catch(() => {
      });

    if (!isInboundMessage(req.body)) {
      let chatRoomRef!: firestore.DocumentReference;

      const getAvailableChatRooms = await projectRef
        .collection("chat_rooms")
        .where("contacts", "array-contains", phoneNumber)
        .get();

      if (!getAvailableChatRooms.empty) {
        getAvailableChatRooms.forEach((result) => {
          chatRoomRef = result.ref;
        });

        const messageRef = chatRoomRef
          .collection("chats")
          .doc(messageId);

        let isExist = false;
        let checkTotal = 0;

        while (!isExist && checkTotal < 4) {
          checkTotal++;
          const getMessage = await messageRef.get();
          isExist = getMessage.exists;
          if (!isExist)
            await new Promise((resolve) => {
              setTimeout(() => {
                resolve(true);
              }, 5000);
            });
        }
        if (!isExist && checkTotal >= 4) {
          const errorJson = {
            success: false,
            messages: "Message Document Not Found",
            messageId: messageId,
          };
          console.error(errorJson);
          return res.status(500).send(errorJson);
        }
        const getMessage = await messageRef.get();
        const batch = myFirestore.batch();

        if (
          req.body.statuses[0].errors &&
          req.body.statuses[0].errors.length > 0
        ) {
          batch.update(messageRef, {
            error: req.body.statuses[0].errors[0],
            "statuses.failed": firestore.Timestamp.fromMillis(
              parseInt(req.body.statuses[0].timestamp) * 1000
            ),
          });
          batch.update(chatRoomRef, {
            "recent_chat.statuses.failed":
              firestore.Timestamp.fromMillis(
                parseInt(req.body.statuses[0].timestamp) * 1000
              ),
          });
        } else {
          const updateField = {} as any;
          const updateFieldRecentChat = {} as any;
          updateField[`statuses.${status}`] =
            firestore.Timestamp.fromMillis(
              parseInt(req.body.statuses[0].timestamp) * 1000
            );
          updateFieldRecentChat[`recent_chat.statuses.${status}`] =
            firestore.Timestamp.fromMillis(
              parseInt(req.body.statuses[0].timestamp) * 1000
            );
          batch.update(messageRef, updateField);
          batch.update(chatRoomRef, updateFieldRecentChat);
        }

        const dataMessageDocument =
          getMessage.data() as IFirestoreMessageEntity;
        if (dataMessageDocument.bindContextAndDocuments) {
          for (const bindContextAndDocument of dataMessageDocument.bindContextAndDocuments) {
            const docRef =
              bindContextAndDocument.path as firestore.DocumentReference;
            batch.update(docRef, {
              "whatsapp.statuses": dataMessageDocument.statuses,
            });
          }
        }

        try {
          await batch.commit();
        } catch (e) {
          const errorJson = {
            success: false,
            messages: "Failed to commit to firestore.",
            messageId: messageId,
          };
          console.error(errorJson);
          return res.status(500).send(errorJson);
        }
      } else {
        const errorJson = {
          success: false,
          messages: "Chat Room Not Found",
          messageId: messageId,
        };
        console.error(errorJson);
        return res.status(500).send(errorJson);
      }

      return res.send({
        success: true,
      });
    } else {
      return res.status(500).send({
        success: false,
      });
    }
  }
}

export default QiscusWebhookHandler;
