import {j<PERSON>, Request, RequestH<PERSON><PERSON>, Response} from "express";
import {myFirestore} from "../../services/google/firebaseAdmin";
import {body} from "express-validator";
import {ISessionMessageRef, QiscussWebhook} from "../../types/webhook/qiscuss";
import {firestore} from "firebase-admin";
import mime from "mime";

import {clientHandlerHelper} from "../../helper/webhook/client";
import {chatRoomHandlerHelper} from "../../helper/webhook/chatRoom";
import {messageContextHandlerHelper} from "../../helper/webhook/messageContext";

import {IQiscusReceivedMessageBody} from "../../types/services/qiscus/received_message_types";
import {IFirestoreMessageEntity} from "../../types/firestore/message_types";
import payloadValidator from "../middlewares/payloadValidator";
import offerService from "../../services/OfferService";
import {IGetOffer} from "../../types/services/offerService.types";

interface ReqBody extends QiscussWebhook {
  templateName: string;
  area: string;
  bindDocuments?: IFirestoreMessageEntity['bindContextAndDocuments'];
  notes?: string;

  vehicle?: {
    model_name: string;
    variant_code: string;
    variant_name: string;
    color_name?: string | null;
    color_code?: string | null;
    year?: string | null;
  };
  offer?: {
    purchaseScheme: "credit" | "cash",
    createdAt: string; // YYYY-MM-DD HH:mm
    offerCode: string;
  }
}

export default class WebhookQiscusHandlerAutoReply {
  public static middlewares: RequestHandler[] = [
    json(),
    body('payload.message.created_at').toDate(),
    body('payload.message.timestamp').toDate(),
    body('payload.room.id'),

    body('template_name').optional(),
    body('area').optional(),
    body('notes').optional({values: "falsy"}),
    payloadValidator,
  ];

  public static async handler(req: Request<{}, any, ReqBody>, res: Response) {
    console.log(JSON.stringify(req.body))
    const batch = myFirestore.batch();
    const projectRef = myFirestore.collection('projects').doc('v8GGopG1v89nd46aajEX');

    let clientRef!: firestore.DocumentReference;
    let chatRoomRef!: firestore.DocumentReference;

    let name = req.body.payload.from.name ?? req.body.payload.from.email;

    let sessionType: ISessionMessageRef = {
      sessionType: null,
    }


    let offerData: IGetOffer['data'] | null = null;
    if (req.body.offer?.offerCode) {
      try {
        offerData = await offerService.getByOfferCode(req.body.offer.offerCode);
      } catch (e) {
      }
    }

    try {
      const messageContext = () => {
        let messageContext: IQiscusReceivedMessageBody = {
          type: "text",
        } as any;
        const {message} = req.body.payload;
        if (message.type === "file_attachment") {
          const mediaPayload = message.payload!;
          const type = mime.getType(mediaPayload.url);
          switch (type?.split('/')[0]) {
            case "image":
              messageContext.type = "image";
              messageContext['image'] = {
                link: mediaPayload.url,
                caption: mediaPayload.caption,
                id: "",
              }
              break;
            default:
              messageContext.type = "image";
              messageContext['image'] = {
                link: mediaPayload.url,
                caption: mediaPayload.caption,
                id: "",
              }
              break;
          }
        } else if (req.body.payload.message.type === "text") {
          messageContext['text'] = {
            body: req.body.payload.message.text,
          };
        }

        return messageContext;
      }

      const clientHandler = await clientHandlerHelper({
        phoneNumber: req.body.payload.from.email,
        batch: batch,
        name: name,
        messageContext: messageContext(),
        cityGroup: req.body.area ?? undefined,
        autoReply: true,
        offerData: offerData ?? undefined,
        vehicle: req.body.vehicle ? {
          ...req.body.vehicle,
          year: null,
          buy_time: null,
        } : undefined,
        notes: req.body.notes,
      }, sessionType)

      clientRef = clientHandler.clientRef;

      let chatRoomLabelRef;
      if (req.body.templateName) {
        switch (req.body.templateName) {
          case "credit_confirm":
          case "order_confirm2":
            chatRoomLabelRef = myFirestore.doc("/projects/v8GGopG1v89nd46aajEX/labels/18BIn8vK76qZOVlvWnFx");
            break;
          case "received_update2":
            chatRoomLabelRef = myFirestore.doc("/projects/v8GGopG1v89nd46aajEX/labels/KLlkiVcGXDeQveFpeXaI");
            break;
          case "stnk_notif":
            chatRoomLabelRef = myFirestore.doc("/projects/v8GGopG1v89nd46aajEX/labels/cGrFulpCL5OHieYk4Ebg");
            break;
          case "leads_welcome_1":
            chatRoomLabelRef = myFirestore.doc("/projects/v8GGopG1v89nd46aajEX/labels/NW5xSTMA59FKYsalXzKo");
            break;
          case "cdb_template_ver3":
            chatRoomLabelRef = myFirestore.doc("/projects/v8GGopG1v89nd46aajEX/labels/TT8vEjkK5DQ7ydaUOeO2");
            break;
          case "finco_order_notif_ver2":
          case "finco_order_notif_ver3":
            chatRoomLabelRef = myFirestore.doc("/projects/v8GGopG1v89nd46aajEX/labels/GLqSyTEfx73VsYKWQCl7");
            break;
        }
      }

      const chatRoomHandler = await chatRoomHandlerHelper({
        projectRef: projectRef,
        batch: batch,
        name: clientHandler.name || name,
        phoneNumber: req.body.payload.from.email,
        date: req.body.payload.message.created_at,
        clientRef: clientRef,
        messageContext: messageContext(),

        cityGroup: req.body.area ?? undefined,
        labelRef: chatRoomLabelRef ?? undefined,
        vehicle: req.body.vehicle ? {
          ...req.body.vehicle,
          year: null,
          buy_time: null,
          color_name: req.body.vehicle.color_name || null,
          color_code: req.body.vehicle.color_code || null,
        } : undefined,
      }, true);

      chatRoomRef = chatRoomHandler.chatRoomRef;

      // const sessionHandler = await sessionHandlerHelper({
      //     roomRef: chatRoomRef,
      //     messageId: req.body.payload.message.unique_temp_id,
      //     name: name,
      //     phoneNumber: req.body.payload.from.email,
      //     clientRef: clientRef,
      //     batch: batch,
      //     date: req.body.payload.message.created_at,
      //     messageContext: messageContext(),
      //     isFromTemplate: true,
      // }, sessionType);

      const messageRef = await messageContextHandlerHelper({
        roomRef: chatRoomRef,
        sessionId: "",
        messageId: req.body.payload.message.unique_temp_id,
        name: clientHandler.name || name,
        phoneNumber: req.body.payload.from.email,
        clientRef: clientRef,
        batch: batch,
        date: req.body.payload.message.created_at,
        messageContext: messageContext(),
        bindDocuments: req.body.bindDocuments,
      }, true)


      batch.set(myFirestore.collection("phone_number_crm_template").doc(req.body.payload.message.unique_temp_id), {
        created_at: firestore.Timestamp.now(),
        message_ref: messageRef.messageRef,
        target_phone_number: req.body.payload.from.email,
        template_name: req.body.templateName,
        provider: "qiscus",
      }, {
        merge: true,
      });

      await batch.commit();

      if (req.body.bindDocuments && req.body.bindDocuments?.length > 0) {
        for (const bindDocument of req.body.bindDocuments) {
          if (bindDocument.context === "add_raw_leads") {
            const docRef = myFirestore.doc(bindDocument.path as string);
            docRef.update({
              "whatsapp.messageId": messageRef.document.message.id,
              "whatsapp.idealPath": messageRef.messageRef,
              "whatsapp.statuses": messageRef.document.statuses,
              "whatsapp.response": null,
            }).then()
          }
        }
      }


      res.send({
        success: true,
      })
    } catch (e: any) {
      console.log(e)
      res.status(500).send({
        success: false,
        data: JSON.stringify(e),
      })
    }

  }
}
