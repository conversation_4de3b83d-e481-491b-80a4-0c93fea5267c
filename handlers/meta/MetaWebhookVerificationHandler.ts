import {NextFunction, Request, Response} from "express";
import {query, validationResult} from "express-validator";

interface ReqParams {
    "hub.mode": "subscribe";

    "hub.challenge": number;

    "hub.verify_token": string;
}


const MetaWebhookVerificationHandler = {
    middlewares: [
        query("hub.mode").notEmpty(),
        query("hub.challenge").notEmpty(),
        query("hub.verify_token").notEmpty().custom((input, meta) => {
            const META_KEY = process.env.META_WEBHOOK_KEY;
            if (input !== META_KEY) return false;
            else return true;
        }).withMessage("Verifikasi Token Tidak Sesuai"),
        function (req: Request, res: Response, next: NextFunction) {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({errors: errors.array()});
            } else {
                next();
            }
        }
    ],
    handler: function (req: Request<any, any, any, ReqParams>, res: Response) {
        res.send(req.query["hub.challenge"]);
    }
}

export default MetaWebhookVerificationHandler;