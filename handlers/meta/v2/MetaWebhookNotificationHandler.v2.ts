import { json, NextFunction, Request, Response } from "express";
import moment from "moment/moment";

import { myFirestore } from "../../../services/google/firebaseAdmin";
import {
	ISessionMessageRef,
	TSessionOfUser,
} from "../../../types/webhook/qiscuss";
import { firestore } from "firebase-admin";

import idealServices from "../../../services/ideal_backend/idealServices";
import { IFirestoreMessageEntity } from "../../../types/firestore/message_types";
import startConversationFlowVer4 from "../../../autoReplyFlow/ver4/startConversationFlow.ver4";
import flowOnWebhookVer4 from "../../../autoReplyFlow/ver4/flowOnWebhook.ver4";
import { chatRoomHandlerHelper } from "../../../helper/webhook/chatRoom";
import { clientHandlerHelper } from "../../../helper/webhook/client";
import logAdSourceId from "../../../helper/webhook/logAdSourceId";
import { messageContextHandlerHelper } from "../../../helper/webhook/messageContext";
import sendMessageSlowResponse from "../../../helper/webhook/sendMessageSlowResponse";
import { sessionHandlerHelper } from "../../../helper/webhook/sessionContext";
import setClientCreditSchemeFromAdSourceId from "../../../helper/webhook/setClientCreditSchemeFromAdSourceId";
import {
	MetaWebhookNotificationBase,
	Value,
} from "../../../types/webhook/meta_webhook_noitification.types";
import createLeadsFromInboundMessage from "../../../services/ideal_backend/createLeadsFromInboundMessage";
import sendMessageWithAi from "../../../helper/webhook/sendMessageWithAi";
import {ProjectDocumentTypes} from "../../../types/firestore/projectDocumentTypes";

/*
 * Di versi ini project dibuat dinamis
 * */

const MetaWebhookNotificationHandler = {
	middlewares: [
		json(),
		async function (
			req: Request<any, any, MetaWebhookNotificationBase>,
			res: Response,
			next: NextFunction
		) {
			console.log(
				"META_WEBHOOK_NOTIFICATION_V2",
				JSON.stringify(req.body)
			);
			let body: Value = req.body.entry[0].changes[0].value;
			try {
				if (body.messages) {
					const phoneNumber =
						body.contacts?.[0].wa_id ?? body.messages[0].from;
					const id = body.messages[0].id;
					const docPath = myFirestore
						.collection("whatsapp_webhook_log")
						.doc(phoneNumber)
						.collection("meta")
						.doc(id);
					await docPath.set({
						...req.body,
						type: "message",
					});
				} else if (body.statuses) {
					const phoneNumber = body.statuses[0].recipient_id;
					const id = body.statuses[0].id;
					const docPath = myFirestore
						.collection("whatsapp_webhook_log")
						.doc(phoneNumber)
						.collection("meta")
						.doc(id);
					await docPath.set({
						...req.body,
						type: "statuses",
					});
				}
			} catch (e) {}
			next();
		},
	],
	handler: async function (
		req: Request<any, any, MetaWebhookNotificationBase>,
		res: Response
	) {
		const sessionType: ISessionMessageRef = {
			sessionType: TSessionOfUser.EXTEND_SESSION,
		};

		const projectId = req.params.projectId;

		const findProject = await myFirestore
			.collection("projects")
			.doc(projectId)
			.get();

		if (!findProject.exists) {
			return res.status(500).send({
				success: false,
				message: "Project tidak dapat ditemukan",
			});
		}

		const projectRef = findProject.ref;
    const projectData = findProject.data() as ProjectDocumentTypes;

		let clientRef!: firestore.DocumentReference;
		let chatRoomRef!: firestore.DocumentReference;

		let body!: Value;

		try {
			body = req.body.entry[0].changes[0].value;
		} catch (e) {
			return res.status(500).send({
				success: false,
			});
		}

		if ("messages" in body) {
			let conversationFlowStarted = false;
			const messageBody = body.messages[0];
			const phoneNumber =
				body.contacts?.[0].wa_id ?? body.messages[0].from;
			const messageDate = moment
				.unix(parseInt(messageBody.timestamp))
				.toDate();

			try {
				let name = body.contacts?.[0].profile.name ?? phoneNumber;

				if (messageBody.text?.body) {
					await createLeadsFromInboundMessage({
						messageContent: messageBody.text.body,
						phoneNumber: phoneNumber,
						name: name,
					});
				}

				let firestoreBatch = myFirestore.batch();

				const clientHandler = await clientHandlerHelper(
					{
						phoneNumber: phoneNumber,
						batch: firestoreBatch,
						name: name,
						messageContext: messageBody,
					},
					sessionType
				);

				clientRef = clientHandler.clientRef;

				const chatRoomHandler = await chatRoomHandlerHelper({
					projectRef: projectRef,
					batch: firestoreBatch,

					name: clientHandler.name || phoneNumber,
					phoneNumber: phoneNumber,

					date: messageDate,

					clientRef: clientRef,

					messageContext: messageBody,

					cityGroup: !!clientHandler.city
						? clientHandler.city
						: undefined,
          agentAiReplyNewConversation: Boolean(projectData.agent_ai_reply),
				});

				chatRoomRef = chatRoomHandler.chatRoomRef;

				const sessionHandler = await sessionHandlerHelper(
					{
						roomRef: chatRoomRef,
						messageId: messageBody.id,
						name: name,
						phoneNumber: phoneNumber,
						clientRef: clientRef,
						batch: firestoreBatch,
						date: messageDate,
						messageContext: messageBody,
					},
					sessionType
				);

				await messageContextHandlerHelper({
					roomRef: chatRoomRef,
					sessionId: sessionHandler.sessionId,
					messageId: messageBody.id,
					name: name,
					phoneNumber: phoneNumber,
					clientRef: clientRef,
					batch: firestoreBatch,
					date: messageDate,
					messageContext: messageBody,
				});

				await firestoreBatch.commit();



				try {
					if (messageBody.context?.id) {
						const messageRef = chatRoomRef
							.collection("chats")
							.doc(messageBody.context.id);
						const getMessageDocument = await messageRef.get();
						const dataMessageDocument =
							getMessageDocument.data() as IFirestoreMessageEntity;

						if (dataMessageDocument.bindContextAndDocuments) {
							for (const bindContextAndDocument of dataMessageDocument.bindContextAndDocuments) {
								const docRef =
									bindContextAndDocument.path as firestore.DocumentReference;
								if (
									bindContextAndDocument.context ===
									"add_leads"
								) {
									const responseText =
										messageBody.button?.text ??
										"MESSAGE_FAILED_TO_FETCH";
									docRef
										.update({
											"whatsapp.response": {
												text: responseText,
												repliedAt: messageDate,
											},
										})
										.then()
										.catch();
									// idealServices.notifyReplyLeads({
									//     path: docRef.path,
									//     messageId: messageBody.context?.id ?? "",
									//     createdAt: messageDate,
									//     text: messageBody.button?.text ?? "MESSAGE_FAILED_TO_FETCH"
									// }).then()
									//     .catch()
								} else if (
									bindContextAndDocument.context ===
									"add_raw_leads"
								) {
									const docRef =
										bindContextAndDocument.path as firestore.DocumentReference;
									const responseText =
										messageBody.button?.text ??
										"MESSAGE_FAILED_TO_FETCH";
									docRef
										.update({
											"whatsapp.response": {
												text: responseText,
												repliedAt: messageDate,
											},
										})
										.then()
										.catch();

									if (responseText === "Pembelian Kredit") {
										await startConversationFlowVer4({
											phoneNumber: phoneNumber,
											startAt: "leadsCredit",
											chatRoom: chatRoomRef,
											projectDoc: projectRef,
										});
										conversationFlowStarted = true;
									}
								}
							}
						}
					}
				} catch (e) {}


        /*
        * Matikan dulu slow response dan auto reply karena akan digantikan oleh AI
        * */

				// await sendMessageSlowResponse({
				// 	phoneNumber: phoneNumber,
				// 	chatRoomRef: chatRoomRef,
				// 	clientRef: clientRef,
				// 	session: sessionType,
				// 	projectRef: projectRef,
				// }).catch(() => {});

				// try {
				// 	if (messageBody.referral && !chatRoomHandler.data.conversation_flow_executed) {
				// 		await setClientCreditSchemeFromAdSourceId({
				// 			clientRef: clientRef,
				// 			adSourceId: messageBody.referral.source_id,
				// 			projectRef: projectRef,
				// 		});
				// 		await logAdSourceId({
				// 			sourceId: messageBody.referral.source_id,
				// 			phoneNumber: phoneNumber,
				// 			sessionType: sessionType.sessionType,
				// 			projectRef: projectRef,
				// 		});
				// 		await startConversationFlowVer4({
				// 			startAt: "referralSourceId",
				// 			chatRoom: chatRoomRef,
				// 			phoneNumber: phoneNumber,
				// 			referralSource: {
				// 				sourceId: messageBody.referral.source_id,
				// 			},
				// 			projectDoc: projectRef,
				// 		});
				// 		conversationFlowStarted = true;
        //
				// 		await chatRoomRef.update({
				// 			conversation_flow_executed: true,
				// 		});
        //
				// 	} else if (clientHandler.isNewUser) {
				// 		await startConversationFlowVer4({
				// 			startAt: "newCustomer",
				// 			chatRoom: chatRoomRef,
				// 			phoneNumber: phoneNumber,
				// 			projectDoc: projectRef,
				// 		});
				// 		conversationFlowStarted = true;
				// 	}
        //
				// 	if (!conversationFlowStarted) {
				// 		await flowOnWebhookVer4({
				// 			chatRoom: chatRoomRef,
				// 			phoneNumber: phoneNumber,
				// 			message: messageBody,
				// 			projectDoc: projectRef,
				// 		});
				// 	}
				// } catch (e) {}

        await sendMessageWithAi({
          chatRoomRef: chatRoomRef,
          clientRef: clientRef,
          phoneNumber: phoneNumber,
          projectRef: projectRef,
          replyMessageId: messageBody.id,
        })

				res.send({
					success: true,
				});
			} catch (e) {
				res.status(500).send({
					success: false,
					data: JSON.stringify(e),
				});
			}
		}

		if ("statuses" in body) {
			const phoneNumber = body.statuses[0].recipient_id;
			const statuses = body.statuses[0];

			const messageId = statuses.id;
			const status = statuses.status;

			try {
				const collection = myFirestore.collection(
					"phone_number_crm_template"
				);
				const doc = collection.doc(messageId);

				await doc.set(
					{
						[status]: firestore.Timestamp.fromMillis(
							parseInt(statuses.timestamp) * 1000
						),
					},
					{
						merge: true,
					}
				);
			} catch (e) {
				console.log(e);
			}

			try {
				const getAvailableChatRooms = await projectRef
					.collection("chat_rooms")
					.where("contacts", "array-contains", phoneNumber)
					.get();

				if (getAvailableChatRooms.empty) {
					return res.send({
						success: true,
					});
				}

				getAvailableChatRooms.forEach((result) => {
					chatRoomRef = result.ref;
				});

				const messageRef = chatRoomRef
					.collection("chats")
					.doc(messageId);

				if (
					statuses.errors &&
					statuses.errors.length > 0
				) {
					await messageRef.update({
						error: statuses.errors[0],
						"statuses.failed": firestore.Timestamp.fromMillis(
							parseInt(statuses.timestamp) * 1000
						),
					});
					await chatRoomRef.update({
						"recent_chat.statuses.failed":
							firestore.Timestamp.fromMillis(
								parseInt(statuses.timestamp) * 1000
							),
					});
				} else {
					const updateField = {} as any;
					const updateFieldRecentChat = {} as any;
					updateField[`statuses.${status}`] =
						firestore.Timestamp.fromMillis(
							parseInt(statuses.timestamp) * 1000
						);
					updateFieldRecentChat[
						`recent_chat.statuses.${status}`
					] = firestore.Timestamp.fromMillis(
						parseInt(statuses.timestamp) * 1000
					);
					await messageRef.update(updateField);
					await chatRoomRef.update(updateFieldRecentChat);
				}

				const getMessageDocument = await messageRef.get();
				const dataMessageDocument =
					getMessageDocument.data() as IFirestoreMessageEntity;

				if (dataMessageDocument.bindContextAndDocuments) {
					for (const bindContextAndDocument of dataMessageDocument.bindContextAndDocuments) {
						const docRef =
							bindContextAndDocument.path as firestore.DocumentReference;
						docRef
							.update({
								"whatsapp.statuses":
									dataMessageDocument.statuses,
							})
							.then()
							.catch();

						if (bindContextAndDocument.context === "add_leads") {
							if (status === "failed") {
								idealServices
									.notifyFailedSendWaLeads({
										path: docRef.path,
									})
									.then()
									.catch();
							}
						}
					}
				}

				res.send({
					success: true,
				});
			} catch (e: any) {
				const collectionErrorStatus = myFirestore.collection(
					"webhook_send_status_error_counter"
				);
				const doc = collectionErrorStatus.doc(messageId);

				const get = await doc.get();

				if (get.exists) {
					const data = get.data()!;
					const counter: number = data["count"];

					if (counter > 3) {
						console.log(
							"META_WEBHOOK_NOTIFICATION_ERROR_STOP_TRY",
							JSON.stringify(req.body)
						);
						await doc.update({
							count: counter + 1,
						});
						res.status(200).send({
							success: false,
							message: e.toString(),
						});
					}
				} else {
					console.log(
						"META_WEBHOOK_NOTIFICATION_ERROR_RETRY",
						JSON.stringify(req.body)
					);
					res.status(500).send({
						success: false,
						message: e.toString(),
					});
				}
			}
		}
	},
};

export default MetaWebhookNotificationHandler;
