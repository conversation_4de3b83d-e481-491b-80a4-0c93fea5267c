import {NextFunction, Request, RequestHandler, Response} from "express";
import {param, query, validationResult} from "express-validator";
import {myFirestore} from "../../../services/google/firebaseAdmin";
import {ProjectDocumentTypes} from "../../../types/firestore/projectDocumentTypes";

interface ReqQuery {
    "hub.mode": "subscribe";
    "hub.challenge": number;
    "hub.verify_token": string;
}

interface ReqParams {
    projectId: string;
}

/*
* Di versi ini project dibuat dinamis
* */
const MetaWebhookVerificationHandler: {
    middlewares: RequestHandler[],
    handler: RequestHandler<ReqParams, any ,any, ReqQuery>,
} = {
    middlewares: [
        param("projectId").notEmpty(),

        query("hub.mode").notEmpty(),
        query("hub.challenge").notEmpty(),
        query("hub.verify_token"),
        function (req: Request, res: Response, next: NextFunction) {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({errors: errors.array()});
            } else {
                next();
            }
        }
    ],
    handler: async function (req: Request<ReqParams, any, any, ReqQuery>, res: Response) {
        const projectId = req.params.projectId;
        const projectCollection = myFirestore.collection("projects");

        const getProject = await projectCollection
            .doc(projectId)
            .get();

        if(!getProject.exists) {
            return res.status(404).send({
                success: false,
                messages: "Project not found.",
            });
        }

        const dataProject = getProject.data() as ProjectDocumentTypes;

        if(dataProject.meta?.verifyToken === req.query["hub.verify_token"]) {
            return res.send(req.query["hub.challenge"]);
        } else {
            return res.status(404).send({
                success: false,
                messages: "Verify Token not match",
            });
        }

    }
}

export default MetaWebhookVerificationHandler;
