import {<PERSON><PERSON><PERSON><PERSON><PERSON>} from "express";
import {firestore} from "firebase-admin";
import moment from "moment";
import {chatRoom<PERSON><PERSON>lerHelper} from "../../../helper/webhook/chatRoom";
import {clientHandler<PERSON>elper} from "../../../helper/webhook/client";
import {messageContextHandlerHelper} from "../../../helper/webhook/messageContext";
import {myFirestore} from "../../../services/google/firebaseAdmin";
import offerService from "../../../services/OfferService";

import {IFirestoreMessageEntity} from "../../../types/firestore/message_types";
import {IGetOffer} from "../../../types/services/offerService.types";

import {TAvailablePostMessageContext} from "../../../types/services/qiscus/qiscus_message_context_types";
import {IQiscusReceivedMessageBody} from "../../../types/services/qiscus/received_message_types";
import {ISessionMessageRef} from "../../../types/webhook/qiscuss";

interface ReqBody {
  targetPhoneNumber: string;
  messageId: string;
  text: string;
  header?: {
    type: string;
    text?: string;
    images?: string;
  },
  templateComponents?: any[];
  name: string;

  templateName: string;
  area: string;
  bindDocuments?: IFirestoreMessageEntity['bindContextAndDocuments'];

  notes?: string;

  vehicle?: {
    model_name: string;
    variant_code: string;
    variant_name: string;
    color_name?: string | null;
    color_code?: string | null;
    year?: string | null;
  };
  offer?: {
    purchaseScheme: "credit" | "cash",
    createdAt: string; // YYYY-MM-DD HH:mm
    offerCode: string;
  }

  projectId: string;
}

const metaWebhookTemplateHandlerV2: {
  middlewares: RequestHandler[];
  handler: RequestHandler<any, any, ReqBody>;
} = {
  middlewares: [],
  handler: async (req, res) => {
    const now = moment();
    const batch = myFirestore.batch();
    const projectRef = myFirestore.collection('projects').doc(req.body.projectId);

    const phoneNumber = req.body.targetPhoneNumber;

    let sessionType: ISessionMessageRef = {
      sessionType: null,
    }

    let offerData: IGetOffer['data'] | null = null;
    if (req.body.offer?.offerCode) {
      await offerService.getByOfferCode(req.body.offer.offerCode).then(data => {
        offerData = data;
      })
        .catch();
    }

    let messageContext!: TAvailablePostMessageContext;
    let textBody!: string;

    if (req.body.header?.images) {
      messageContext = "image";
      textBody = req.body.text || "";
    } else {
      messageContext = "text";
      textBody = req.body.text || "";
    }

    const context: IQiscusReceivedMessageBody = {
      from: phoneNumber,
      id: req.body.messageId,
      type: messageContext,
      text: {
        body: textBody,
      },
      image: {
        id: "",
        caption: textBody,
        link: req.body.header?.images || ""
      },
      timestamp: now.unix().toString(),
    }

    const clientHandler = await clientHandlerHelper({
      phoneNumber: phoneNumber,
      batch: batch,
      name: req.body.name || phoneNumber,
      real_name: req.body.name,
      messageContext: context,
      cityGroup: req.body.area,
      autoReply: true,
      offerData: offerData || undefined,
      vehicle: req.body.vehicle ? {
        ...req.body.vehicle,
        year: null,
        buy_time: null,
      } : undefined,
      notes: req.body.notes,
    }, sessionType)

    let chatRoomLabelRef;
    if (req.body.templateName) {
      if (req.body.projectId === "B6hfkRsnmcXsKUpFYjN8") {
        let projectId = req.body.projectId;
        switch (req.body.templateName) {
          case "received_update":
            chatRoomLabelRef = myFirestore.doc(`/projects/${projectId}/labels/KLlkiVcGXDeQveFpeXaI`);
            break;
          case "stnk_update":
            chatRoomLabelRef = myFirestore.doc(`/projects/${projectId}/labels/cGrFulpCL5OHieYk4Ebg`);
            break;
        }
      } else if (req.body.projectId === "WLdKug7hau0MbRzKcnqg") {
        let projectId = req.body.projectId;
        if (req.body.projectId === projectId) {
          switch (req.body.templateName) {
            case "leads1":
              chatRoomLabelRef = myFirestore.doc(`/projects/${projectId}/labels/NW5xSTMA59FKYsalXzKo`);
              break;
          }
        }
      }
    }

    const chatRoomHandler = await chatRoomHandlerHelper({
      projectRef: projectRef,
      batch: batch,
      name: clientHandler.name || phoneNumber,
      phoneNumber: phoneNumber,
      date: now.toDate(),
      clientRef: clientHandler.clientRef,
      messageContext: context,
      cityGroup: req.body.area || undefined,
      labelRef: chatRoomLabelRef,
      vehicle: req.body.vehicle ? {
        ...req.body.vehicle,
        year: null,
        buy_time: null,
        color_name: req.body.vehicle.color_name || null,
        color_code: req.body.vehicle.color_code || null,
      } : undefined,
    }, true);

    const message = await messageContextHandlerHelper({
      roomRef: chatRoomHandler.chatRoomRef,
      sessionId: "",
      messageId: req.body.messageId,
      name: clientHandler.name || phoneNumber,
      phoneNumber: phoneNumber,
      clientRef: clientHandler.clientRef,
      batch: batch,
      date: now.toDate(),
      messageContext: context,
      bindDocuments: req.body.bindDocuments ?? [],
      template: {
        name: req.body.templateName,
        components: req.body.templateComponents || [],
      },
    }, true)

    batch.set(myFirestore.collection("phone_number_crm_template").doc(req.body.messageId), {
      created_at: firestore.Timestamp.now(),
      message_ref: message.messageRef,
      target_phone_number: phoneNumber,
      template_name: req.body.templateName,
      provider: "meta",
    }, {
      merge: true,
    });

    await batch.commit();

    if (req.body.bindDocuments && req.body.bindDocuments?.length > 0) {
      for (const bindDocument of req.body.bindDocuments) {
        if (bindDocument.context === "add_leads") {
          const docRef = myFirestore.doc(bindDocument.path as string);
          docRef.update({
            "whatsapp.messageId": message.document.message.id,
            "whatsapp.idealPath": message.messageRef,
            "whatsapp.statuses": message.document.statuses,
            "whatsapp.response": null,
          }).then()
        }
      }
    }


    res.send({
      success: true,
    })
  }
}

export default metaWebhookTemplateHandlerV2;
