import {Request, Response} from "express";
import {myFirestore} from "../services/google/firebaseAdmin";
import {ISessionLog, ISessionLogDataClient} from "../types/firestore/session_log_types";
import {IFirestoreMessageEntity} from "../types/firestore/message_types";
import {clientSessionLogBigquery} from "../services/google/bigQueryService";
import moment from "moment";
import {IClientDocument} from "../types/firestore/client_collection_types";
import {IChatRoomDocument} from "../types/firestore/chat_room_document_types";
import collect from "collect.js";
import {firestore} from "firebase-admin";


export default class ClientLogHandler {
    public static middlewares = [];
    private static smallHelpers = {
        countReplyTime: (messages: Pick<IFirestoreMessageEntity, "message" | "origin">[]) => {
            const replyTime: number[] = [];

            let processWhenDirection: IFirestoreMessageEntity['message']['direction'] = "IN";
            let messageIn!: Pick<IFirestoreMessageEntity, "message" | "origin">;
            let messageOut!: Pick<IFirestoreMessageEntity, "message" | "origin">;
            for (const [, message] of messages.entries()) {
                if (message.message.direction === processWhenDirection) {
                    if (processWhenDirection === "IN") {
                        processWhenDirection = "OUT";
                        messageIn = message;
                    } else if (processWhenDirection === "OUT") {
                        processWhenDirection = "IN";
                        messageOut = message;
                        replyTime.push(messageOut.message.unixtime - messageIn.message.unixtime);
                    }
                }
            }

            return replyTime;
        },
        inOutTextChars: (messages: Pick<IFirestoreMessageEntity, "message" | "origin">[]): {
            inboundText: number;
            outboundText: number
        } => {
            let inboundText = 0, outboundText = 0;
            for (const [, message] of messages.entries()) {
                let count = 0;

                if (message.message.type === "text") {
                    count = message.message.text?.body.length ?? 0;
                } else {
                    count = message.message.image?.caption?.length ?? 0
                }

                if (message.message.direction === "IN") inboundText += count;
                else outboundText += count;
            }

            return {
                inboundText,
                outboundText,
            }
        },
        inOutLink: (messages: Pick<IFirestoreMessageEntity, "message" | "origin">[]): {
            inboundLinkCount: number;
            outboundLinkCount: number;
            inboundLink: string[]
        } => {
            let containRefLink: string[] = []
            let inboundLink = 0, outboundLink = 0;
            for (const [, message] of messages.entries()) {
                let text!: string;

                if (message.message.type === "text") {
                    text = message.message.text?.body ?? "";
                } else {
                    text = message.message.image?.caption ?? "";
                }

                const expression = /(https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|www\.[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9]+\.[^\s]{2,}|www\.[a-zA-Z0-9]+\.[^\s]{2,})/gi;
                const regExp = new RegExp(expression);
                const match = regExp.exec(text);
                if (match && match?.length >= 1) {
                    if (message.message.direction === "IN") {
                        containRefLink.push(match[0]);
                        inboundLink++;
                    } else outboundLink++;
                }
            }

            return {
                inboundLinkCount: inboundLink,
                outboundLinkCount: outboundLink,
                inboundLink: containRefLink,
            }
        }
    }

    public static async handler(req: Request, res: Response) {
        const logCollections = myFirestore.collection('client_session_logs')
        const logSessionLogs: ISessionLog<ISessionLogDataClient>[] = []

        const threshold = moment().set({hours: 23, minutes: 59});
        const now = moment();

        const getLogDocuments = await logCollections
            .where('auto_end_session_at', '>=', threshold.toDate())
            .get()

        const payloadBigQuery: ISessionLogDataClient[] = [];

        try {
            getLogDocuments.forEach(snapshot => {
                const data = snapshot.data() as ISessionLog<ISessionLogDataClient>;
                const firstMessageDate = moment(data.messages[0].message.timestamp.toDate());
                if(firstMessageDate.isBefore(threshold)) {
                    logSessionLogs.push({
                        ...data,
                        sessionId: snapshot.id,
                        ref: snapshot.ref,
                    });
                }

            });

            const collectSessionLogs = collect<ISessionLog<ISessionLogDataClient>>(logSessionLogs);
            const chunkSessionLogCollections = collectSessionLogs.chunk(450);

            const batches: firestore.WriteBatch[] = [];

            for (const sessionLogsChunkPart of chunkSessionLogCollections.all()) {
                for (const data of sessionLogsChunkPart) {

                    const batch = myFirestore.batch();
                    batches.push(batch);

                    let analyticData: ISessionLogDataClient = {} as any;
                    let messages = data.messages;
                    let inboundMessages = messages.filter(value => value.message.direction === "IN")
                    let outboundMessages = messages.filter(value => value.message.direction === "OUT")

                    try {
                        const clientId = inboundMessages[0]?.origin.id || data.customerPhoneNumber;

                        if(!clientId) {
                            continue;
                        }

                        analyticData.inbound = inboundMessages.length;
                        analyticData.outbound = outboundMessages.length;

                        analyticData.client_id = clientId;
                        analyticData.session_start = inboundMessages?.[0].message.timestamp.toDate() || outboundMessages[0].message.timestamp.toDate();

                        analyticData.uuid = data.sessionId;
                        analyticData.session_end = messages[messages.length - 1].message.timestamp.toDate();

                        const inOutTextChars = ClientLogHandler.smallHelpers.inOutTextChars(messages);
                        const inOutLink = ClientLogHandler.smallHelpers.inOutLink(messages);

                        analyticData.inbound_text = inboundMessages
                            .filter(value => value.message.type === "text" || (value.message.type === "image" && value.message.image?.caption)).length;
                        analyticData.outbound_text = outboundMessages
                            .filter(value => value.message.type === "text" || (value.message.type === "image" && value.message.image?.caption)).length;

                        analyticData.list_inbound_link = inOutLink.inboundLink;

                        analyticData.inbound_img = inboundMessages.filter(value => value.message.type === "image").length;
                        analyticData.outbound_img = outboundMessages.filter(value => value.message.type === "image").length;

                        analyticData.avg_inbound_txt_chars = analyticData.inbound_text >= 1 ? inOutTextChars.inboundText / analyticData.inbound_text : 0;
                        analyticData.avg_inbound_txt_chars = Math.ceil(analyticData.avg_inbound_txt_chars)

                        analyticData.avg_outbound_txt_chars = analyticData.outbound_text >= 1 ? inOutTextChars.outboundText / analyticData.outbound_text : 0;
                        analyticData.avg_outbound_txt_chars = Math.ceil(analyticData.avg_outbound_txt_chars);

                        const replyTime: number[] = ClientLogHandler.smallHelpers.countReplyTime(messages);

                        analyticData.first_time_resp = Math.ceil(replyTime?.[0] ?? 0);

                        analyticData.min_time_resp = replyTime.length >= 1 ? Math.ceil(Math.min(...replyTime)) : 0;
                        analyticData.max_time_resp = replyTime.length >= 1 ? Math.ceil(Math.max(...replyTime)) : 0;
                        analyticData.avg_time_resp = replyTime.length >= 1 ? replyTime.reduce((previousValue, currentValue) => previousValue + currentValue) / replyTime.length : 0;
                        analyticData.avg_time_resp = Math.ceil(analyticData.avg_time_resp);

                        analyticData.inbound_link = inOutLink.inboundLinkCount;
                        analyticData.outbound_link = inOutLink.outboundLinkCount;
                        analyticData.created_at = now.toDate();
                        // analyticData.messages = messages.map(value => ({
                        //     ref_path: data.chat_room.path + `/chats/${value.message.id}`,
                        //     direction: value.message.direction,
                        // }))

                        analyticData.messages = [];

                        analyticData.model = null;
                        analyticData.variant = null;
                        analyticData.color = null;

                        analyticData.label = null;
                        analyticData.department = null;

                        const getClientDoc = await inboundMessages[0].origin.reference?.get();
                        if(getClientDoc?.exists) {
                            const clientData = getClientDoc.data()! as IClientDocument;
                            if(clientData.dream_vehicle) {
                                if(clientData.dream_vehicle.model_name) {
                                    analyticData.model = {
                                        name: clientData.dream_vehicle.model_name,
                                    }
                                }
                                if(clientData.dream_vehicle.variant_code) {
                                    analyticData.variant = {
                                        name: clientData.dream_vehicle.variant_name,
                                        code: clientData.dream_vehicle.variant_code,
                                    }
                                }
                                if(clientData.dream_vehicle.color_code && clientData.dream_vehicle.color_name) {
                                    analyticData.color = {
                                        name: clientData.dream_vehicle.color_name,
                                        code: clientData.dream_vehicle.color_code,
                                    }
                                }
                            }
                        }

                        const getChatRoomDoc = await data.chat_room.get();
                        const chatRoomData = getChatRoomDoc.data() as IChatRoomDocument;

                        const getProjectDoc = await data.chat_room.parent.parent?.get();

                        analyticData.provider = getProjectDoc?.get('provider');
                        analyticData.projectName = getProjectDoc?.get('legal_name');
                        analyticData.projectId = getProjectDoc?.id || "";
                        analyticData.projectPhoneNumber = getProjectDoc?.get("phone_number") || "";

                        if(getClientDoc?.exists) {
                            if(chatRoomData.label) {
                                const getLabel = await chatRoomData.label.get();
                                if(getLabel.exists) {
                                    analyticData.label = getLabel.get("name");
                                }
                            }

                            if(chatRoomData.doc_department) {
                                const getDepartment = await chatRoomData.doc_department.get();
                                if(getDepartment.exists) {
                                    analyticData.department = getDepartment.get("name");
                                }
                            }
                        }

                        payloadBigQuery.push(analyticData);

                        batch.update(data.ref, {
                            data_analytic: analyticData,
                            sent_to_bigquery_at: now,
                        })
                    } catch (e: any) {
                        console.log(e, inboundMessages, outboundMessages)
                    }

                }
            }

            if (payloadBigQuery.length >= 1) {
                await clientSessionLogBigquery.insert(payloadBigQuery);
                for (const batch of batches) {
                    await batch.commit();
                }
            }

            console.log("Inserted to big query :", payloadBigQuery.length)

            res.send({
                success: true,
            })
        } catch (e: any) {
            console.log(e)
            res.send({
                success: false,
                message: e.toString(),
            })
        }

    }
}
