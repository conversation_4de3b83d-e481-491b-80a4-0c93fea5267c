import {json, Request, Response} from "express";
import {body} from "express-validator";
import payloadValidator from "../middlewares/payloadValidator";
import {myFirestore} from "../../services/google/firebaseAdmin";
import {ProjectDocumentTypes} from "../../types/firestore/projectDocumentTypes";
import {firestore} from "firebase-admin";
import {clientHandlerHelper} from "../../helper/webhook/client";
import {chatRoomHandlerHelper} from "../../helper/webhook/chatRoom";
import {firestoreService} from "../../services/google/FirestoreService";
import {IQiscusReceivedMessageBody} from "../../types/services/qiscus/received_message_types";
import phoneNumberCountryCodeSanitizer from "../../helper/phoneNumberCountryCodeSanitizer";
import triforceCheckinHelper from "../../helper/webhook/triforce/triforceCheckinHelper";

interface ReqBody {
    messageId: string;
    phoneNumber: string;
    contactName?: string;
    message?: Omit<IQiscusReceivedMessageBody, 'from' | 'id' | 'timestamp'> & {
        date: Date;
        context?: {
            type: "checkin",
            checkin?: {
                count: number;
            } | null
        } | null
    };
    status?: {
        read: Date;
    }
}

const triforceInboundMessageHandler = {
    middlewares: [
        json(),
        body("messageId").notEmpty().isUUID(),
        body("contactName").optional(),
        body("phoneNumber").notEmpty().customSanitizer(
            input => {
                return phoneNumberCountryCodeSanitizer(input, "62", "62");
            }
        ),

        // Make message optional, but when provided, validate required fields
        body("message").optional(),
        body("message.date").if(body("message").exists()).notEmpty()
            .isISO8601().withMessage("message.date must be a valid date")
            .toDate(),
        body("message.type").if(body("message").exists()).notEmpty().isIn([
            "text", "image", "video", "audio", "document", "voice",
            "location", "sticker", "button", "interactive", "system",
            "context", "contacts", "referral"
        ]),
        body("message.context").if(body("message").exists()).optional().isObject(),
        body("message.context.type").if(body("message.context").exists()).notEmpty().isIn(["checkin"]),

        // Conditional validation based on message type
        body("message.context.type.checkin").if(body("message.context.type").equals("checkin")).optional().isObject(),
        body("message.context.type.checkin.count").if(body("message.context.type").equals("checkin")).optional().isInt(),
        body("message.text.body").if(body("message.type").equals("text")).notEmpty(),
        body("message.image").if(body("message.type").equals("image")).isObject(),
        body("message.image.caption").if(body("message.type").equals("image")).optional(),
        body("message.video").if(body("message.type").equals("video")).isObject(),
        body("message.video.caption").if(body("message.type").equals("video")).optional(),
        body("message.audio").if(body("message.type").equals("audio")).isObject(),
        body("message.audio.caption").if(body("message.type").equals("audio")).optional(),
        body("message.document").if(body("message.type").equals("document")).isObject(),
        body("message.document.caption").if(body("message.type").equals("document")).optional(),
        body("message.voice").if(body("message.type").equals("voice")).isObject(),
        body("message.voice.caption").if(body("message.type").equals("voice")).optional(),
        body("message.locations.address").if(body("message.type").equals("location")).notEmpty(),
        body("message.locations.latitude").if(body("message.type").equals("location")).isNumeric(),
        body("message.locations.longitude").if(body("message.type").equals("location")).isNumeric(),
        body("message.locations.name").if(body("message.type").equals("location")).notEmpty(),
        body("message.sticker").if(body("message.type").equals("sticker")).isObject(),
        body("message.sticker.id").if(body("message.type").equals("sticker")).notEmpty(),
        body("message.button").if(body("message.type").equals("button")).isObject(),
        body("message.button.payload").if(body("message.type").equals("button")).notEmpty(),
        body("message.button.text").if(body("message.type").equals("button")).notEmpty(),
        body("message.interactive").if(body("message.type").equals("interactive")).isObject(),
        body("message.interactive.type").if(body("message.type").equals("interactive")).isIn(["list_reply", "button_reply"]),
        body("message.system.body").if(body("message.type").equals("system")).notEmpty(),
        body("message.context").if(body("message.type").equals("context")).isObject(),
        body("message.context.from").if(body("message.type").equals("context")).notEmpty(),
        body("message.context.id").if(body("message.type").equals("context")).notEmpty(),

        // Make status optional, but when provided, validate required fields
        body("status").optional(),
        body("status.read").if(body("status").exists()).notEmpty()
            .isISO8601().withMessage("status.read must be a valid date")
            .toDate(),

        payloadValidator,
    ],
    handler: async function (req: Request<any, any, ReqBody>, res: Response) {
        const projectRef = myFirestore
            .collection("projects")
            .doc("xMRPksjoItk5u9KvwfIB");

        const projectDoc = await projectRef.get();
        if (!projectDoc.exists) {
            return res.status(500).send({
                success: false,
                message: "Project tidak ditemukan",
            });
        }

        const projectData = projectDoc.data() as ProjectDocumentTypes;

        let clientRef!: firestore.DocumentReference;
        let chatRoomRef!: firestore.DocumentReference;


        if (req.body.message) {
            let firestoreBatch = myFirestore.batch();
            // Create message context based on message type
            const createMessageContext = () => {
                const baseContext = {
                    id: req.body.messageId,
                    type: req.body.message!.type,
                    timestamp: (Date.now() / 1000).toString(),
                    from: req.body.phoneNumber,
                };

                // Add type-specific content
                switch (req.body.message!.type) {
                    case 'text':
                        return {
                            ...baseContext,
                            text: {
                                body: req.body.message!.text?.body || '',
                            },
                        };
                    case 'image':
                        return {
                            ...baseContext,
                            image: req.body.message!.image,
                        };
                    case 'video':
                        return {
                            ...baseContext,
                            video: req.body.message!.video,
                        };
                    case 'audio':
                        return {
                            ...baseContext,
                            audio: req.body.message!.audio,
                        };
                    case 'document':
                        return {
                            ...baseContext,
                            document: req.body.message!.document,
                        };
                    case 'voice':
                        return {
                            ...baseContext,
                            voice: req.body.message!.voice,
                        };
                    case 'location':
                        return {
                            ...baseContext,
                            locations: req.body.message!.locations,
                        };
                    case 'sticker':
                        return {
                            ...baseContext,
                            sticker: req.body.message!.sticker,
                        };
                    case 'button':
                        return {
                            ...baseContext,
                            button: req.body.message!.button,
                        };
                    case 'interactive':
                        return {
                            ...baseContext,
                            interactive: req.body.message!.interactive,
                        };
                    case 'system':
                        return {
                            ...baseContext,
                            system: req.body.message!.system,
                        };
                    case 'context':
                        return {
                            ...baseContext,
                            context: req.body.message!.context,
                        };
                    default:
                        return {
                            ...baseContext,
                            text: {
                                body: '',
                            },
                        };
                }
            };

            const messageContext = createMessageContext();

            const clientHandler = await clientHandlerHelper({
                phoneNumber: req.body.phoneNumber,
                batch: firestoreBatch,
                name: req.body.contactName || req.body.phoneNumber,
                real_name: req.body.contactName,
                messageContext,
            }, {
                sessionType: null,
            });

            clientRef = clientHandler.clientRef;

            // Parse message date string to Date object
            const messageDate = new Date(req.body.message.date);

            const chatRoomHandler = await chatRoomHandlerHelper({
                projectRef: projectRef,
                batch: firestoreBatch,
                name: clientHandler.name || req.body.phoneNumber,
                phoneNumber: req.body.phoneNumber,
                date: messageDate,
                clientRef: clientRef,
                messageContext,
                cityGroup: !!clientHandler.city ? clientHandler.city : undefined,
            });

            chatRoomRef = chatRoomHandler.chatRoomRef;

            // Create message object for firestore based on message type
            const createFirestoreMessage = () => {
                const baseMessage = {
                    id: req.body.messageId,
                    type: req.body.message!.type,
                    timestamp: firestore.Timestamp.fromDate(messageDate),
                    unixtime: messageDate.getTime() / 1000,
                    direction: "IN" as const,
                };

                // Add type-specific content for firestore
                switch (req.body.message!.type) {
                    case 'text':
                        return {
                            ...baseMessage,
                            text: {
                                body: req.body.message!.text?.body || '',
                            },
                        };
                    default:
                        return {
                            ...baseMessage,
                            ...req.body.message,
                            timestamp: firestore.Timestamp.fromDate(messageDate),
                            unixtime: messageDate.getTime() / 1000,
                            direction: "IN" as const,
                        };
                }
            };

            await firestoreService.addNewMessage(
                {
                    message: createFirestoreMessage(),
                    statuses: {
                        delivered: null,
                        read: null,
                        sent: null,
                        failed: null,
                        deleted: null,
                    },
                    origin: {
                        display_name: clientHandler.name || req.body.phoneNumber,
                        id: req.body.phoneNumber,
                        reference: clientRef,
                        system_reply: false,
                    },
                    session_id: null,
                    bindContextAndDocuments: [],
                    bindDocuments: [],
                    isTemplate: false,
                },
                chatRoomRef,
                req.body.messageId,
                firestoreBatch
            )

            await firestoreBatch.commit();

            if (req.body.message?.context?.type === "checkin" && req.body.message?.context?.checkin?.count) {
                await triforceCheckinHelper({
                    roomRef: chatRoomRef,
                    clientRef: clientRef,
                    batch: firestoreBatch,
                    phoneNumber: req.body.phoneNumber,
                    checkin: req.body.message.context.checkin,
                    messageId: req.body.messageId,
                })
            }
        } else if (req.body.status) {
            let firestoreBatch = myFirestore.batch();
            const chatRoom = await projectRef.collection("chat_rooms")
                .doc(req.body.phoneNumber)
                .get();

            if (chatRoom.exists) {
                chatRoomRef = chatRoom.ref;
                const messageRef = chatRoomRef.collection("chats").doc(req.body.messageId);

                firestoreBatch.update(messageRef, {
                    "statuses.read": firestore.Timestamp.fromDate(req.body.status.read),
                });
            }
            await firestoreBatch.commit();
        }


        res.send({
            success: true,
        });
    },
}

export default triforceInboundMessageHandler;
