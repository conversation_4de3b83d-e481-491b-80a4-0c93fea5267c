import {json, NextFunction, Request, Response} from "express";
import {myFirestore} from "../../services/google/firebaseAdmin";
import {firestore} from "firebase-admin";
import {ISessionMessageRef} from "../../types/webhook/qiscuss";
import {profileService} from "../../services/profileService";
import {clientHandlerHelper} from "../../helper/webhook/client";
import {chatRoomHandlerHelper} from "../../helper/webhook/chatRoom";
import {sessionHandlerHelper} from "../../helper/webhook/sessionContext";
import {messageContextHandlerHelper} from "../../helper/webhook/messageContext";
import {
    IBaseQiscusReceivedMessage,
    IQiscusReceivedMessageBody
} from "../../types/services/qiscus/received_message_types";
import {IBaseQiscusReceivedNotification} from "../../types/services/qiscus/notification_message_types";
import {isStatuses} from "../../types/webhook/webhook_message_statuses_types";
import {isInboundMessage} from "../../types/webhook/all_new_webhook_types";

const projectId = "db9131ab-8a5d-48fa-8c99-2ff78695b5a4";

export default class WebhookKataAiHandler {
    public static middlewares = [
        json(),
        (req: Request, res: Response, next: NextFunction) => {
            myFirestore.collection('log_webhook_kata.ai_collection').add(req.body).then();
            next();
        },
    ];

    public static async handler(req: Request<any, any, IBaseQiscusReceivedMessage | IBaseQiscusReceivedNotification>, res: Response, next: NextFunction) {
        const projectRef = myFirestore.collection('projects').doc(projectId);
        let chatRoomRef!: firestore.DocumentReference;

        if (isInboundMessage(req.body)) {
            let name = req.body.contacts[0].profile.name;
            let date = new Date(parseInt(req.body.messages[0].timestamp) * 1000);
            let messageId = req.body.messages[0].id;

            const batch = myFirestore.batch();
            try {

                const sessionType: ISessionMessageRef = {
                    sessionType: null,
                };

                let clientRef!: firestore.DocumentReference;

                const phoneNumber = req.body.contacts[0].wa_id;

                const clientHandler = await clientHandlerHelper({
                    phoneNumber: phoneNumber,
                    batch: batch,
                    name: name,
                    messageContext: {
                        ...(req.body.messages[0] as IQiscusReceivedMessageBody),
                    },
                }, sessionType)

                clientRef = clientHandler.clientRef;

                const chatRoomHandler = await chatRoomHandlerHelper({
                    projectRef: projectRef,
                    batch: batch,

                    name: name,
                    phoneNumber: phoneNumber,

                    date: date,

                    clientRef: clientRef,

                    messageContext: {
                        ...(req.body.messages[0] as IQiscusReceivedMessageBody),
                    },
                    cityGroup: !!clientHandler.city ? clientHandler.city : undefined,
                });

                chatRoomRef = chatRoomHandler.chatRoomRef;


                const sessionHandler = await sessionHandlerHelper({
                    roomRef: chatRoomRef,
                    messageId: messageId,
                    name: name,
                    phoneNumber: phoneNumber,
                    clientRef: clientRef,
                    batch: batch,
                    date: date,
                    messageContext: {
                        ...(req.body.messages[0] as IQiscusReceivedMessageBody),
                    },
                }, sessionType);

                await messageContextHandlerHelper({
                    roomRef: chatRoomRef,
                    sessionId: sessionHandler.sessionId,
                    messageId: messageId,
                    name: name,
                    phoneNumber: phoneNumber,
                    clientRef: clientRef,
                    batch: batch,
                    date: date,
                    messageContext: {
                        ...(req.body.messages[0] as IQiscusReceivedMessageBody),
                    },
                })

                await batch.commit();

                await profileService.updateProfile({
                    phone: req.body.contacts[0].wa_id,
                    customerName: name,
                    cityGroup: !!clientHandler.city ? clientHandler.city.toUpperCase() : null,
                }, req.body.contacts[0].wa_id)

                res.send({
                    success: true,
                });

            } catch (e: any) {
                res.send({
                    success: false,
                    data: e.toString(),
                })
            }
        } else {
            next();
        }
    }

    public static async handler2(req: Request<any, any, IBaseQiscusReceivedMessage | IBaseQiscusReceivedNotification>, res: Response) {
        const projectRef = myFirestore.collection('projects').doc(projectId);
        let chatRoomRef!: firestore.DocumentReference;

        if (isStatuses(req.body)) {
            const chatRooms = await projectRef.collection('chat_rooms')
                .where('contacts', 'array-contains', req.body.statuses[0].recipient_id)
                .get();

            if (!chatRooms.empty) {
                chatRooms.forEach(result => {
                    chatRoomRef = result.ref;
                });

                const messageRef = chatRoomRef.collection('chats').doc(req.body.statuses[0].id)
                if (req.body.statuses[0].errors && req.body.statuses[0].errors.length > 0) {
                    await messageRef.update({
                        error: req.body.statuses[0].errors[0],
                        "statuses.failed": firestore.Timestamp.fromMillis(parseInt(req.body.statuses[0].timestamp) * 1000),
                    })
                    await chatRoomRef.update({
                        "recent_chat.statuses.failed": firestore.Timestamp.fromMillis(parseInt(req.body.statuses[0].timestamp) * 1000),
                    })
                } else {
                    const updateField = {} as any;
                    const updateFieldRecentChat = {} as any;
                    updateField[`statuses.${req.body.statuses[0].status}`] = firestore.Timestamp.fromMillis(parseInt(req.body.statuses[0].timestamp) * 1000);
                    updateFieldRecentChat[`recent_chat.statuses.${req.body.statuses[0].status}`] = firestore.Timestamp.fromMillis(parseInt(req.body.statuses[0].timestamp) * 1000);
                    await messageRef.update(updateField)
                    await chatRoomRef.update(updateFieldRecentChat)
                }
            }
        }

        res.send({
            success: true,
        })
    }

}
