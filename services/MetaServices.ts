import axios from "axios";
import {GetTemplateResponseMeta} from "../types/services/meta/GetTemplateResponse.meta";
import {IPayloadQiscusPostMessage} from "../types/services/qiscus/send_message_types";
import {IResponseSendMessage} from "../types/services/meta/SendMessageResponse.meta";

class MetaServices {
    private metaBaseUrl = axios.create({
        baseURL: "https://graph.facebook.com/v20.0/",
    });

    async sendMessage(params: {
        payload: IPayloadQiscusPostMessage,
        meta: {
            phoneNumberId: string;
            bearerToken: string;
        },
    }) {
        return await this.metaBaseUrl.post<IResponseSendMessage>(`${params.meta.phoneNumberId}/messages`, {
            messaging_product: "whatsapp",
            ...params.payload
        }, {
            headers: {
                "Authorization": `Bearer ${params.meta.bearerToken}`,
            }
        });
    }

    getTemplate(
        params: {
            meta: {
                whatsappBusinessAccountId: string;
                bearerToken: string;
            },
            query?: {
                name?: string;
            }
        }
    ) {
        return this.metaBaseUrl.get<GetTemplateResponseMeta>(`/${params.meta.whatsappBusinessAccountId}/message_templates`, {
            headers: {
                "Authorization": `Bearer ${params.meta.bearerToken}`,
            }
        });
    }
}

const metaServices = new MetaServices();

export default metaServices;
