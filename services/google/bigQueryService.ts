import {BigQuery} from "@google-cloud/bigquery";

export const bigQuery = new BigQuery({
    keyFilename: './keys/ideal-trimitra-26d125bc4a2c.json',
    projectId: 'ideal-trimitra'
})

export const adminSessionLogBigquery = bigQuery.dataset("chat_analytics").table("admin_sessions");
export const clientSessionLogBigquery = bigQuery.dataset("chat_analytics").table("user_sessions");

// Tidak Terpakai
// export const labelBigQuery = bigQuery.dataset("chat_analytics").table("labeling_aggregate");

export const priceListTable = bigQuery
    .dataset("pricelist_log")
    .table("pricelist_activitylog");
