import {myFirestore} from "./firebaseAdmin";
import {IClientDocument} from "../../types/firestore/client_collection_types";
import {v4} from "uuid";
import {IFirestoreMessageEntity} from "../../types/firestore/message_types";
import {firestore} from "firebase-admin";
import {IChatRoomDocument} from "../../types/firestore/chat_room_document_types";

class FirestoreService {
  public readonly clientCollection = myFirestore.collection('clients');

  public async addNewClient(params: IClientDocument, batch?: firestore.WriteBatch) {
    const docRef = this.clientCollection.doc(v4());
    if (batch) {
      batch.set(docRef, params);
    } else {
      await docRef.set(params);
    }
    return docRef;
  }

  public async addNewChatRoom(params: IChatRoomDocument, roomDocRef: FirebaseFirestore.DocumentReference, batch?: FirebaseFirestore.WriteBatch) {
    const chatRoomRef = roomDocRef;
    if (batch) {
      batch.set(chatRoomRef, params);
    } else {
      await chatRoomRef.set(params)
    }
    return chatRoomRef;
  }

  public async addNewMessage(params: IFirestoreMessageEntity, chatRoomRef: firestore.DocumentReference, messageId?: string, batch?: firestore.WriteBatch) {
    const messageRef = chatRoomRef.collection('chats').doc(messageId ?? v4());
    if (batch) {
      batch.set(messageRef, params);
    } else {
      await messageRef.set(params);
    }
    return messageRef;
  }

  public async getChats(chatRoom: firestore.DocumentReference) {
    let query = chatRoom.collection('chats')
        .orderBy('message.timestamp', 'asc');
    const get = await query.get();
    return get.docs.map(doc => doc.data() as IFirestoreMessageEntity);
  }

  public async getChatRoomData(chatRoomPath: firestore.DocumentReference) {
    const get = await chatRoomPath.get();
    return get.data() as IChatRoomDocument;
  }

  public async getClientFromChatRoom(chatRoom: firestore.DocumentReference) {
    const get = await chatRoom.get();
    const data = get.data() as IChatRoomDocument;
    const client = data.clients[0];
    const getClient = await client.get();
    return getClient.data() as IClientDocument;
  }
}

export const firestoreService = new FirestoreService();
