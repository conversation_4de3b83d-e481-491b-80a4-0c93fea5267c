import axios, {AxiosError} from "axios";
import moment from "moment";
import {IQiscusResponsePostMessage,} from "../types/services/qiscus/qiscus_message_context_types";
import {IPayloadQiscusPostMessage, IQiscusSendMessageParams} from "../types/services/qiscus/send_message_types";

class QiscusServiceNew {
    private activeToken?: string;
    private lastLogin?: Date;

    private email: string = "<EMAIL>";
    private password: string = "4martahondaQ123";

    private appId = "novi-r1k2gj0hgdkessf4";
    private secretKey = "ca9bd4078b15a0905dbfdfd2eee75e4a";
    private whatsappChannelId = "856";

    private readonly qiscusPostBaseUrl = axios.create({
        baseURL: `https://multichannel.qiscus.com/whatsapp/v1/${this.appId}/${this.whatsappChannelId}`,
        headers: {
            "Qiscus-App-Id": this.appId,
            "Qiscus-Secret-Key": this.secretKey,
        }
    });

    private readonly qiscusAuthBaseUrl = axios.create({
        baseURL: 'https://multichannel.qiscus.com/api/v1/auth',
    });

    public async loginFirst(): Promise<void> {
        if (!this.activeToken) {
            await this.login();
        } else {
            const expiredTokenDate = moment(this.lastLogin).add(29, "days");
            if (moment().isAfter(expiredTokenDate)) {
                await this.login();
            }
        }
    }

    public async sendMessageNative(params: IPayloadQiscusPostMessage) {
        await this.loginFirst();

        const body: IPayloadQiscusPostMessage = JSON.parse(JSON.stringify(params));

        if (body.text) {
            body.text.body = body.text.body.replace(/(<br>|<\/br>|<br \/>)/mgi, "\n");
        }

        try {
            return await this.qiscusPostBaseUrl.post<IQiscusResponsePostMessage>('/messages', body);
        } catch (e: any) {
            throw e as AxiosError;
        }
    }

    public async sendMessage(params: IQiscusSendMessageParams) {
        await this.loginFirst();

        let sendParam: IPayloadQiscusPostMessage = {
            "to": params.target,
            "recipient_type": "individual",
            "type": "text",
            "preview_url": false
        }

        if (params.imageUrl) {
            sendParam.image = {
                link: params.imageUrl,
                caption: params.text ?? "",
            }
            sendParam.type = "image";
        } else {
            if (params.target === '6285763705624') {
                sendParam.text = {
                    body: params.text + " - Developer" || "",
                }
            } else {
                sendParam.text = {
                    body: params.text ?? "",
                }
            }
        }

        try {
            const send = await this.qiscusPostBaseUrl.post<IQiscusResponsePostMessage>('/messages', sendParam);
            return send;
        } catch (e: any) {
            const error = e as AxiosError;
            throw e;
        }
    }

    public async getImage(mediaId: string) {
        await this.loginFirst();
        return await this.qiscusPostBaseUrl.get('/media/' + mediaId, {
            responseType: "arraybuffer",
        });
    }

    private async login() {
        try {
            const login = await this.qiscusAuthBaseUrl.post('', {
                email: this.email,
                password: this.password,
            });
            this.activeToken = login.data.data.user.authentication_token;
            this.lastLogin = new Date();
            this.qiscusPostBaseUrl.defaults.headers.common = {
                'Authorization': this.activeToken ?? "",
                'app_id': this.appId,
            }
        } catch (e: any) {
            throw e as AxiosError;
        }


    }
}

export const qiscusServiceNew = new QiscusServiceNew();
