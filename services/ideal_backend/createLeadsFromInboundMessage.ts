import { isAxiosError } from "axios";
import { IAgentRefCode } from "../../types/firestore/agentRefCode.types";
import { AddLeadReqBody } from "../../types/services/ideal.service.types";
import { myFirestore } from "../google/firebaseAdmin";
import idealServices from "./idealServices";
import {firestore} from "firebase-admin";

const isMessageContainsAgentRefCode = (messageContent: string) => {
	const refCodePattern = /TFC#[A-Z0-9]{6}#REF/;
	const match = messageContent.match(refCodePattern);

	if (match) {
		return { code: match[0] }; // Mengembalikan refCode yang ditemukan
	}
	throw new Error("No valid refCode found in message");
};

const createLeadsFromInboundMessage = async (params: {
	messageContent: string;
	phoneNumber: string;
	name: string;
}) => {
	try {
		const { messageContent, phoneNumber, name } = params;
		const agentRefCodeCollections =
			myFirestore.collection("agent_ref_codes");

		// Extract agent ref code data
		let agentRefCode;
		try {
			agentRefCode = isMessageContainsAgentRefCode(messageContent);
		} catch (error) {
			return;
		}

		const agentRefCodeDocRef = agentRefCodeCollections.doc(
			agentRefCode.code
		);
		const agentRefCodeDoc = await agentRefCodeDocRef.get();

		if (!agentRefCodeDoc.exists) {
			console.error(
				"[createLeadsFromInboundMessage] Agent ref code not found:",
				agentRefCode.code
			);
			return;
		}


		const agentRefCodeData = agentRefCodeDoc.data() as IAgentRefCode;

		// Prepare lead data
		const leadData: AddLeadReqBody = {
			agentCode: agentRefCodeData.agentCode,
			agentName: agentRefCodeData.agentName,
			title: "Kakak",
			firstName:
				phoneNumber !== agentRefCodeData.phoneNumber
					? name
					: agentRefCodeData.fullName,
			lastName: "",
			phoneNumber:
				phoneNumber !== agentRefCodeData.phoneNumber
					? phoneNumber
					: agentRefCodeData.phoneNumber,
			email: "",
			provinceName: agentRefCodeData.province.name,
			provinceCode: agentRefCodeData.province.code,
			cityName: agentRefCodeData.city.name,
			cityCode: agentRefCodeData.city.code,
			vehicleUsage: "individual",
			paymentPlan: "cash",
			hasVehicleLoan: false,
			vehicleOptions: [
				{
					brand: { name: agentRefCodeData.vehicle.brand.name },
					model: { name: agentRefCodeData.vehicle.model.name },
					variant: {
						code: agentRefCodeData.vehicle.variant.code,
						name: agentRefCodeData.vehicle.variant.name,
					},
					color: {
						code: agentRefCodeData.vehicle.color.code,
						name: agentRefCodeData.vehicle.color.name,
					},
				},
			],
			source: `${agentRefCodeData.source}-qrcode`,
			source2: agentRefCodeData.organization,
			purchasePlan: "firstVehicle",
			nextTotalVehicleOwnerShip: "1",
			notes: agentRefCodeData.notes || "Add Leads From Inbound Message",
			idCard_number: null,
			driverLicense_number: null,
		};

		await idealServices.addLeads(leadData);

		await agentRefCodeDocRef.update({
			isLeadsSent: true,
			createdLeads: firestore.FieldValue.arrayUnion({
				phoneNumber: leadData.phoneNumber,
				createdAt: firestore.Timestamp.now(),
			})
		});
	} catch (error) {
		console.error(error);
		if (isAxiosError(error)) {
			console.error(
				"[createLeadsFromInboundMessage] Unexpected error:",
				JSON.stringify(error.response?.data, null, 2)
			);
		} else {
			console.error(
				"[createLeadsFromInboundMessage] Unexpected error:",
				error
			);
		}
	}
};

export default createLeadsFromInboundMessage;
