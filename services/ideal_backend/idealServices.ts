import axios from "axios";
import {AddLeadReqBody} from "../../types/services/ideal.service.types";

let baseUrl =
  "https://asia-southeast2-ideal-trimitra.cloudfunctions.net/ideal-backend";

// if (process.env.NODE_ENV === "development") {
//   baseUrl = "http://localhost:8000";
// }

class IdealServices {
  private base = axios.create({
    baseURL: baseUrl,
  });

  public notifyReplyLeads(body: {
    path: string;
    messageId: string;
    text: string;
    createdAt: Date;
  }) {
    return this.base.post("/leads/webhook/notify-whatsapp-reply", {
      ...body,
    });
  }

  public notifyRawLeadsReply(body: {
    path: string;
    messageId: string;
    text: string;
    createdAt: Date;
  }) {
    return this.base.post(
      "/leads/webhook/notify-whatsapp-reply/raw-leads",
      {...body}
    );
  }

  public notifyFailedSendWaLeads(body: { path: string }) {
    return this.base.post("/leads/webhook/notification-whatsapp-failed", {
      ...body,
    });
  }

  public addLeads(body: AddLeadReqBody) {
    return this.base.post("/leads/add-lead", {...body}, {
      headers: {
        Authorization: this.basicAuthForLeads(),
      },
    });
  }

  public sendMessage(body: {
    senderId: string;
    senderName: string;
    roomPath: string;
    phoneNumber: string;
    replyMessageId?: string;
    text: string;
    buttons?:string;
  }) {
    const formData = new FormData();
    formData.append("senderId", body.senderId);
    formData.append("senderName", body.senderName);
    formData.append("roomPath", body.roomPath);
    formData.append("phoneNumber", body.phoneNumber);
    formData.append("text", body.text);
    if (body.buttons) {
      formData.append("buttons", body.buttons);
    }
    if (body.replyMessageId) {
      formData.append("replyMessageId", body.replyMessageId);
    }
    return this.base.post("/api/public/message/send", formData);
  }

  public async getMessage(leadId: string) {
    return this.base.get(`/leads/${leadId}`);
  }

  private basicAuthForLeads = () => {
    const username = "leadsClientApp";
    const password = "pDy<A2T.s(f3`6HZ";
    const base64Auth = Buffer.from(`${username}:${password}`).toString("base64");
    return `Basic ${base64Auth}`;
  };
}

const idealServices = new IdealServices();

export default idealServices;
