import {
    CatalogueBaseResponse,
    GetAvailableAreaResponse,
    GetAvailableModelResponse,
    GetAvailableVariant,
    GetDetailProductResponse,
    Model,
    VariantCatalogue,
    VariantProduct
} from "../types/services/catalaogueTypes";
import {create} from "apisauce";
import {DealDetailResponse} from "../types/services/offerService.types";

class CatalogueServices {
    private readonly _baseUrl = "https://zvu1c5uoue.execute-api.ap-southeast-1.amazonaws.com/v1";
    private readonly _baseAxios = create({
        baseURL: this._baseUrl,
        headers: {
            "x-api-key": "TTliO9UPlx7qvPGLt73Y838gsdSvqhlY4QlUJYGe"
        }
    });

    public getAvailableArea(query?: {cityCode?: string;}) {
        try {
            const queries: any = {};
            if(query?.cityCode) queries.city_code = query?.cityCode;
            return this._baseAxios.get<GetAvailableAreaResponse>('/area/amarta/city', queries);
        } catch (e: any) {
            throw new Error(e);
        }
    }

    public getAvailableModel(params: { city: string }) {
        try {
            return this._baseAxios.get<GetAvailableModelResponse>(`products/amarta/city/${params.city}/model`);
        } catch (e: any) {
            throw new Error(e);
        }
    }

    public getAvailableVariant(params: { model: string, area: string }) {
        try {
            return this._baseAxios.get<GetAvailableVariant>(`products/amarta/area/${params.area}/variant`, {
                model_name: params.model,
            });
        } catch (e: any) {
            throw new Error(e);
        }
    }

    public productVariantArea(params: { area: string, variantCode: string }) {
        try {
            return this._baseAxios.get<CatalogueBaseResponse<VariantProduct[]>>(`products/amarta/area/${params.area}/variant`, {
                code: params.variantCode,
            });
        } catch (e: any) {
            throw new Error(e);
        }
    }

    public detailProduct(params: { variantCode: string }) {
        try {
            return this._baseAxios.get<GetDetailProductResponse>(`detail-products/amarta`, {
                code: params.variantCode,
            });
        } catch (e: any) {
            throw new Error(e);
        }
    }

    public async getUsedVehicle(params: { area: string; licensePlate?: string }) {
        let queries: { license_plate?: string; } = {};

        if (params.licensePlate) queries.license_plate = params.licensePlate;

        const get = await this._baseAxios.get<CatalogueBaseResponse<VariantProduct[]>>(`/products/amarta/citygroup/${params.area}/used`, {
            ...queries,
        });
        if (!get.ok) {
            throw get;
        } else {
            return get.data;
        }
    }

    public async getVariantByCityGroup(params: { area: string; variantCode?: string; modelName?: string; }) {
        let queries: { code?: string; model_name?: string } = {};

        if (params.variantCode) queries.code = params.variantCode;
        if (params.modelName) queries.model_name = params.modelName;

        const get = await this._baseAxios.get<CatalogueBaseResponse<VariantProduct[] | VariantCatalogue[]>>(`/products/amarta/citygroup/${params.area}/variant`, {
            ...queries,
        });
        if (!get.ok) {
            throw get;
        } else {
            return get.data;
        }
    }

    public async getModelByAreaAMH(params: { area: string; }) {
        const get = await this._baseAxios.get<CatalogueBaseResponse<Model[]>>(`/products/amarta/citygroup/${params.area}/model`);
        if (!get.ok) {
            throw get;
        } else {
            return get.data;
        }
    }

    public async getDealData(dealCode: string): Promise<DealDetailResponse['data']> {
        const get = await this._baseAxios.get<DealDetailResponse>(`/deal/amarta/${dealCode}`);
        if(!get.ok) {
            const originalError: any = get.originalError;
            throw Object.assign(
                new Error(originalError.response?.data?.error?.message),
                originalError.response?.data,
            )
        }

        const {data} = get;
        if(!data) {
            throw new Error("data not found")
        }

        return data.data;
    }

}

export const catalogueServices = new CatalogueServices();
