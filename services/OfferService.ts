import {create} from "apisauce";
import {DealDetailResponse, IGetOffer} from "../types/services/offerService.types";
import {AxiosError} from "axios";

class OfferService {
    private base = create({
        baseURL: "https://42cjbxpaa8.execute-api.ap-southeast-1.amazonaws.com/v1",
        headers: {
            "X-Api-Key": "9bekwohY878MgiMsRL0Wk2Xsgv4QsxtW4jEIuBqb"
        }
    })

    public async getByOfferCode(offerCode: string): Promise<IGetOffer['data']> {
        const get = await this.base.get<IGetOffer>(`/offer/public/${offerCode}?company=amarta`);
        if(!get.ok) {
            throw Object.assign(
                new Error(get.originalError.message),
                get.originalError,
            )
        }

        const {data} = get;
        if(!data) {
            throw new Error("data not found")
        }

        return data.data;
    }

}

const offerService = new OfferService();

export default offerService;
