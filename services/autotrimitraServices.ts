import axios, {AxiosError} from "axios";
import {
    AutotrimitraBaseResponse,
    AutotrimitrayBaseError,
    IVariant,
    IVehicleModel
} from "../types/services/autotrimitra_service_types";

class AutotrimitraServices {
    private axios = axios.create({
        baseURL: 'https://au-api-trimitra-get-65q45htc.ts.gateway.dev/',
    });
    private key = "AIzaSyAFTemJSnp4h16lYQfITqLD8Ryp9fGNsVg";


    public async getVehicleModelBrand(params?: { brandUuid?: string, modelUuid?: string, category?: string }) {
        const queries = {
            ...(params?.brandUuid && {
                brand_uuid: params.brandUuid,
            }),
            ...(params?.modelUuid && {
                model_uuid: params.modelUuid,
            }),
            ...(params?.category && {
                category: params.category,
            }),
        }
        try {
            return await this.axios.get<AutotrimitraBaseResponse<IVehicleModel[]>>('/vehicle/model', {
                params: {
                    key: this.key,
                    ...queries,
                }
            });
        } catch (e: any) {
            throw e as AxiosError<AutotrimitrayBaseError>;
        }
    }

    public async getVehicleVariantModel(params?: {
        variantUuid?: string;
        modelUuid?: string;
        code?: string;
    }) {
        const queries = {
            ...(params?.variantUuid && {
                variant_uuid: params.variantUuid,
            }),
            ...(params?.modelUuid && {
                model_uuid: params.modelUuid,
            }),
            ...(params?.code && {
                code: params.code,
            }),
        }
        try {
            return await this.axios.get<AutotrimitraBaseResponse<IVariant[]>>('/vehicle/variant', {
                params: {
                    key: this.key,
                    ...queries,
                }
            });
        } catch (e: any) {
            throw e as AxiosError<AutotrimitrayBaseError>;
        }
    }
}

export const autotrimitraServices = new AutotrimitraServices();
