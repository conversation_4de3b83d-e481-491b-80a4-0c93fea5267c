import axios from "axios";
import moment from "moment";
import {IPayloadQiscusPostMessage} from "../types/services/qiscus/send_message_types";
import {IQiscusResponsePostMessage} from "../types/services/qiscus/qiscus_message_context_types";

class KataAiServices {
    private activeToken!: string;
    private lastLogin?: Date;

    private readonly baseKataAi = axios.create({
        baseURL: 'https://api-whatsapp.kata.ai/v1',
    });

    public async login() {
        const login = await this.baseKataAi.post<{ access_token: string }>('/users/login', {
            username: "amartahonda",
            password: "Upw7xC4xvq",
        });

        this.activeToken = login.data.access_token;
        this.lastLogin = new Date();
        this.baseKataAi.defaults.headers.common = {
            'Authorization': 'Bearer ' + this.activeToken,
        }
    }

    public isTokenGood() {
        return !(!this.activeToken || moment(this.lastLogin).add(24, 'hours').isBefore(moment()));
    }

    public async sendMessage(params: { target: string; text?: string; imageUrl?: string; }) {
        if (!this.isTokenGood()) await this.login();
        let sendParam: IPayloadQiscusPostMessage = {
            "to": params.target ?? "",
            "recipient_type": "individual",
            "type": "text",
            "preview_url": false
        }

        if (params.imageUrl) {
            sendParam.image = {
                link: params.imageUrl,
                caption: params.text ?? "",
            }
            sendParam.type = "image";
        } else {
            if (params.target === '6285763705624') {
                sendParam.text = {
                    body: params.text + " - Developer" || "",
                }
            } else {
                sendParam.text = {
                    body: params.text ?? "",
                }
            }
        }

        return await this.baseKataAi.post<IQiscusResponsePostMessage>('/messages', sendParam);
    }
}

export const kataAiServices = new KataAiServices();
