import axios, { AxiosError } from "axios";
import {
    TrimitraAiChatRequest,
    TrimitraAiChatResponse,
    TrimitraAiError
} from "../types/services/trimitraAiAgent.types";

class TrimitraAiAgentServices {
    private baseUrl = "https://trimitra-ai-agent-153502006543.asia-southeast2.run.app";
    private base = axios.create({
        baseURL: this.baseUrl,
        headers: {
            "Content-Type": "application/json",
        },
    });

    /**
     * Send chat message to Trimitra AI Agent
     * @param params - Chat request parameters
     * @returns Promise with chat response
     */
    public async chatIdeal(params: TrimitraAiChatRequest) {
        try {
            const response = await this.base.post<TrimitraAiChatResponse>(
                "/chat/ideal",
                params
            );
            return response.data;
        } catch (error) {
            throw error as AxiosError<TrimitraAiError>;
        }
    }

}

export const trimitraAiAgentServices = new TrimitraAiAgentServices();
