import axios from "axios";
import { FeedEventRequest, GetMediaByArtCodeResponse } from "../../types/services/trimitraArt/trimitraArt.services.types";

class TrimitraArtServices {
	private baseUrl =
		"https://2dkeeytpe6.execute-api.ap-southeast-1.amazonaws.com/v1";
	private base = axios.create({
		baseURL: this.baseUrl,
		headers: {
			"x-api-key": "sM2TXcm8pW3iDwLAqQJ1t4l6ip8ggTwZ4mwUNdK4",
		},
	});

	async getFeed(params: {
		art_category: "feed" | "feed_promo";
		brand_uid: string;
		start?: number;
		length?: number;
	}) {
		const response = await this.base.get("/feed/content-curation", {
			params,
		});
		return response.data;
	}

	async getMediaByArtCode(artCode: string) {
		const response = await this.base.get<GetMediaByArtCodeResponse>(
			`/public/${artCode}`
		);
		return response.data;
	}

	async feedEvent(params: FeedEventRequest) {
		const response = await this.base.post("/feed/webhook", params);
		return response.data;
	}
}

const trimitraArtServices = new TrimitraArtServices();

export default trimitraArtServices;
