import {firestore} from "firebase-admin";
import {IFirestoreMessageEntity} from "../../types/firestore/message_types";
import {firestoreService} from "../../services/google/FirestoreService";
import {IQiscusReceivedMessageBody} from "../../types/services/qiscus/received_message_types";
import {myFirestore} from "../../services/google/firebaseAdmin";

export async function messageContextHandlerHelper(
    params: {
        roomRef: firestore.DocumentReference;

        sessionId: string;
        messageId: string;

        name: string,
        phoneNumber: string,
        clientRef: firestore.DocumentReference;

        date: Date,
        messageContext: IQiscusReceivedMessageBody;

        bindDocuments?: IFirestoreMessageEntity['bindContextAndDocuments'];

        batch: firestore.WriteBatch,

        systemReply?: boolean;
        template?: {
            name: string,
            components: any[],
        }
    },
    autoReply?: boolean
) {
    let {
        roomRef,

        sessionId,
        messageId,

        name,
        phoneNumber,
        clientRef,

        date,
        messageContext,

        batch,
        template,
    } = params;

    const messageParams: IFirestoreMessageEntity = {
        message: {
            ...messageContext,
            direction: autoReply ? "OUT" : "IN",
            unixtime: date.getTime() / 1000,
            timestamp: firestore.Timestamp.fromDate(date),
            id: messageId,
        },
        statuses: {
            delivered: null,
            read: null,
            sent: firestore.Timestamp.now(),
            failed: null,
            deleted: null,
        },
        origin: {
            display_name: autoReply ? "SYSTEM_REPLY" : name,
            id: (autoReply ? null : phoneNumber) as any,
            reference: (autoReply ? null : clientRef) as any,
            system_reply: !!autoReply,
        },
        session_id: sessionId || null,
        bindContextAndDocuments: params.bindDocuments?.map(value => ({
            ...value,
            path: myFirestore.doc(value.path as string)
        })) ?? [],
        bindDocuments: params.bindDocuments?.map(value => myFirestore.doc(value.path as string)) ?? [],
    };

    if(template?.name) {
        messageParams.template = template;
        messageParams.isTemplate = true;
    }

    const messageRef = await firestoreService.addNewMessage(messageParams, roomRef, messageId, batch);

    return {
        messageRef,
        document: messageParams,
    }
}

