import {firestore} from "firebase-admin";
import llmGenerateMessage from "./llmGenerateMessage";
import {IChatRoomDocument} from "../../types/firestore/chat_room_document_types";
import idealServices from "../../services/ideal_backend/idealServices";
import {ProjectDocumentTypes} from "../../types/firestore/projectDocumentTypes";

const sendMessageWithAi = async (params: {
  chatRoomRef: firestore.DocumentReference;
  clientRef: firestore.DocumentReference;
  phoneNumber: string,
  projectRef: firestore.DocumentReference;
  replyMessageId?: string;
}) => {
  const getProject = await params.projectRef.get();
  const getChatRoom = await params.chatRoomRef.get();
  const chatRoomData = getChatRoom.data() as IChatRoomDocument;
  const projectData = getProject.data() as ProjectDocumentTypes;

  if (!Boolean(chatRoomData.agent_ai_reply)) {
    return;
  }

  let organization = "amarta"
  if (projectData.group === "amartamobil") {
    organization = "vinfast"
  }

  const llm = await llmGenerateMessage({
    chatRoomRef: params.chatRoomRef,
    organization: organization,
    replyMessageId: params.replyMessageId,
  });

  if (llm.success && llm.response) {
    await idealServices.sendMessage({
      senderId: "assistant",
      senderName: "Assistant",
      roomPath: params.chatRoomRef.path,
      phoneNumber: params.phoneNumber,
      text: llm.response,
      replyMessageId: llm.replyMessageId || undefined,
    })
  } else {
    // Update agent_ai_reply to false
    await params.chatRoomRef.update({
      agent_ai_reply: false,
    })
  }
}

export default sendMessageWithAi;
