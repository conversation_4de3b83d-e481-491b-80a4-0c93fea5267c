import {firestore} from "firebase-admin";
import {messageContextHandlerHelper} from "./messageContext";
import {myFirestore} from "../../services/google/firebaseAdmin";
import moment from "moment";
import {qiscusServiceNew} from "../../services/QiscusServiceNew";
import {ISessionMessageRef, TSessionOfUser} from "../../types/webhook/qiscuss";

let text = "Pelanggan yang terhormat, berikut kami sampaikan waktu operasional divisi digital amartahonda.com\n\n" +
    "Whatsapp\n" +
    "9-15 April 2024 : Tidak Beroperasi\n" +
    "16 April 2024 : Operasional Normal\n\n" +
    "Website\n"+
    "9-15 April 2024 : Menerima pesanan & pembayaran\n" +
    "16 April 2024 : Menerima pesanan,pembayaran & pengiriman unit\n\n" +
    "Seluruh pengajuan kredit setelah tanggal 7 April 2024 akan di proses di tanggal 16 April 2024.\n" +
    "Pengiriman unit terakhir di tanggal 9 April 2024.\n\n" +
    "Selamat Hari Raya Idul Fitri 1445 H. <PERSON><PERSON>, <PERSON>hon Maaf Lahir dan <PERSON>in.";

const sendMessageChatOff = async (params: {
    chatRoomRef: firestore.DocumentReference;
    clientRef: firestore.DocumentReference;
    phoneNumber: string,
    session: ISessionMessageRef,
    // sessionId: string;
}) => {
    // const getSessionDoc = await myFirestore.doc("/client_session_logs/" + params.sessionId).get();
    // const sessionData = getSessionDoc.data()

    if (
        params.session.sessionType &&
        [TSessionOfUser.NEW_USER, TSessionOfUser.NEW_SESSION].indexOf(params.session.sessionType) === -1
    ) {
        return;
    }

    const now = moment();
    const start = moment("2024-04-09", "YYYY-MM-DD").startOf("day");
    const end = moment("2024-04-15", "YYYY-MM-DD").endOf("day");

    if (!now.isBetween(start, end)) {
        return;
    }

    const sendMessage = await qiscusServiceNew.sendMessage({
        text: text,
        target: params.phoneNumber,
    })

    let messageId: string = sendMessage.data.messages[0].id;

    const batch = myFirestore.batch();
    await messageContextHandlerHelper({
        roomRef: params.chatRoomRef,
        sessionId: "",
        messageId: messageId,
        name: "SYSTEM_REPLY",
        phoneNumber: params.phoneNumber,
        clientRef: params.clientRef,
        batch: batch,
        date: now.toDate(),
        messageContext: {
            id: messageId,
            type: "text",
            text: {
                body: text,
            },
            timestamp: (now.unix() / 1000).toString(),
            from: messageId,
        },
        bindDocuments: [],
    }, true);

    await batch.commit();
}

export default sendMessageChatOff;
