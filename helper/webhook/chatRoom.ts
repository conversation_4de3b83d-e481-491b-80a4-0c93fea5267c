import {firestore} from "firebase-admin";
import {firestoreService} from "../../services/google/FirestoreService";
import {IQiscusReceivedMessageBody} from "../../types/services/qiscus/received_message_types";
import {IChatRoomDocument} from "../../types/firestore/chat_room_document_types";

export async function chatRoomHandlerHelper(params: {
  projectRef: firestore.DocumentReference;

  name: string,
  phoneNumber: string,
  clientRef: firestore.DocumentReference;

  date: Date,
  messageContext: IQiscusReceivedMessageBody;

  batch: firestore.WriteBatch,
  cityGroup?: string;

  labelRef?: firestore.DocumentReference;
  departmentRef?: firestore.DocumentReference;

  vehicle?: IChatRoomDocument['dream_vehicle'];

  organization?: string;
  organization_group?: string;

  agentAiReplyNewConversation?: boolean;

}, isAutoReply?: boolean) {
  let {
    projectRef,
    name, phoneNumber, clientRef,
    messageContext, date,
    batch,
    cityGroup,
    organization,
    organization_group,
    labelRef,
    vehicle,
    agentAiReplyNewConversation,
  } = params;

  let chatRoomDocRef = projectRef.collection('chat_rooms').doc(phoneNumber);
  let chatRoom: firestore.DocumentSnapshot | null = null;
  let roomContactName = name;

  const findRoom = await projectRef.collection('chat_rooms').where('contacts', 'array-contains', phoneNumber).get();
  if (!findRoom.empty) {
    chatRoom = findRoom.docs[0];
  }

  let departmentRef: firestore.DocumentReference | null = null;

  if (cityGroup) {
    const findDepartmentQuery = await projectRef
      .collection('departments')
      .where('city_group', 'array-contains', cityGroup.toUpperCase());

    const findDepartments = await findDepartmentQuery.get();

    findDepartments.forEach((result) => {
      departmentRef = result.ref;
    })
  }

  const recentChat = {
    contact: phoneNumber,
    direction: (isAutoReply ? "OUT" : "IN") as "IN" | "OUT",
    display_name: isAutoReply ? "System" : roomContactName,
    statuses: {
      failed: null,
      sent: null,
      read: null,
      delivered: null,
      deleted: null,
    },
    text: textInMessageContext({messageContext}),
    timestamp: firestore.Timestamp.fromDate(date),
    type: messageContext.type,
    unixtime: date.getTime() / 1000,
  }

  if (chatRoom) {
    batch.update(chatRoom.ref, {
      "headers.title": roomContactName,
      recent_chat: recentChat,
      ...(cityGroup ? {
        cityGroup: cityGroup.toUpperCase()
      } : {}),
      ...(departmentRef ? {doc_department: departmentRef} : {}),
      ...(labelRef ? {
        label: labelRef,
        label_updated_at: firestore.Timestamp.now(),
      } : {}),
      ...(
        vehicle ? {
          dream_vehicle: vehicle,
        } : {}
      ),
      last_inbound: firestore.Timestamp.fromDate(date),
      last_message_type: "IN",
      ...(vehicle ? {dream_vehicle: vehicle} : {}),
    })
  } else {
    await firestoreService.addNewChatRoom({
      clients: [
        clientRef,
      ],
      contacts: [
        phoneNumber,
      ],
      doc_department: departmentRef ?? null,
      headers: {title: roomContactName},
      recent_chat: recentChat,
      label: labelRef ?? null,
      label_updated_at: labelRef ? firestore.Timestamp.now() : null,
      last_inbound: firestore.Timestamp.fromDate(date),
      last_message_type: "IN",
      organization: organization || null,
      organizationUpdatedBy: null,
      organizationUpdatedAt: organization ? firestore.Timestamp.now() : null,
      organizationGroup: organization_group || null,
      cityGroup: cityGroup || null,
      cityGroupUpdatedAt: cityGroup ? firestore.Timestamp.now() : null,
      cityGroupUpdatedBy: null,

      blockReason: null,
      blocked: false,
      dream_vehicle: vehicle || null,
      created_at: firestore.Timestamp.now(),
      conversation_flow_executed: false,
      agent_ai_reply: agentAiReplyNewConversation || false,
      wait_for_answer: null,
    }, chatRoomDocRef, batch);
  }

  return {
    chatRoomRef: chatRoomDocRef,
  }
}

export function textInMessageContext(params: {
  messageContext: IQiscusReceivedMessageBody;
}) {
  let {messageContext} = params;
  let text = "";
  switch (messageContext.type) {
    case "contacts":
      text = "(Message Contact)";
      break;
    case "audio":
      text = "🎵 ";
      text += messageContext.audio?.caption ?? "";
      break;
    case "document":
      text = "📄 ";
      text += messageContext.document?.caption ?? "";
      break;
    case "image":
      text = "🖼️ ";
      text += messageContext.image?.caption ?? "";
      break;
    case "location":
      text = "📍 "
      break;
    case "system":
      break;
    case "text":
      text = messageContext.text?.body ?? "";
      if (text.length > 20) text = text.slice(0, 20) + "...";
      break;
    case "video":
      text = "📹 ";
      text += messageContext.video?.caption ?? "";
      break;
    case "voice":
      text = "🎤 ";
      text += messageContext.voice?.caption ?? "";
      break;
    case "context":
      break;
    case "sticker":
      break;
    case "interactive" :
      if (messageContext.interactive?.button_reply?.title) text = messageContext.interactive?.button_reply?.title;
      else if (messageContext.interactive?.list_reply?.title) text = messageContext.interactive?.list_reply?.title;
      break;
    case "button" :
      text = messageContext.button?.text ?? ""
      break;
  }

  return text;
}
