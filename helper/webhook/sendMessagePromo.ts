import {firestore} from "firebase-admin";
import {ISessionMessageRef} from "../../types/webhook/qiscuss";
import idealServices from "../../services/ideal_backend/idealServices";

let response = "🚀 PROMO SPESIAL HONDA AMARTA 🚀\n" +
    "Saatnya wujudkan motor impianmu dengan cicilan super ringan & syarat mudah!\n" +
    "\n" +
    "🔥 Kredit DP Ringan:\n" +
    "\n" +
    "- Honda Beat cukup DP 500 ribuan\n" +
    "\n" +
    "- Honda Scoopy cukup DP 700 ribuan\n" +
    "\n" +
    "- Honda Vario cukup DP 900 ribuan\n" +
    "\n" +
    "- Honda PCX cukup DP 1 jutaan\n" +
    "\n" +
    "💰 Beli Tunai? Nikmati DISKON hingga Rp 1.000.000\n" +
    "\n" +
    "✨ Kemudahan Kredit di AMARTA:\n" +
    "✅ Cukup bawa KTP & KK\n" +
    "✅ Proses cepat & tanpa ribet\n" +
    "✅ Bisa dibantu survey online\n" +
    "✅ Banyak pilihan tenor cicilan fleksibel\n" +
    "\n" +
    "📍 Dealer Honda AMARTA – Sahabat setia motor Honda-mu.\n" +
    "📲 Klik tombol di bawah untuk langsung chat sales kami & amankan promo ini!"

const sendMessagePromo = async (params: {
    chatRoomRef: firestore.DocumentReference;
    phoneNumber: string,
}) => {
    await idealServices.sendMessage({
        senderId: "SYSTEM",
        senderName: "SYSTEM_REPLY",
        roomPath: params.chatRoomRef.path,
        phoneNumber: params.phoneNumber,
        text: response,
        buttons: JSON.stringify([
            {
                "payload": "Chat Sales",
                "text": "Chat Sales",
            }
        ])
    })
}

export default sendMessagePromo;
