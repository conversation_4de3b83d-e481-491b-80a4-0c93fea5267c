import {firestore} from "firebase-admin";
import {firestoreService} from "../../services/google/FirestoreService";
import {textInMessageContext} from "./chatRoom";
import {catalogueServices} from "../../services/catalogueServices";
import collect from "collect.js";
import {autotrimitraServices} from "../../services/autotrimitraServices";
import {IVariant} from "../../types/services/autotrimitra_service_types";
import {ISessionMessageRef, TSessionOfUser} from "../../types/webhook/qiscuss";
import {IQiscusReceivedMessageBody} from "../../types/services/qiscus/received_message_types";
import {IGetOffer} from "../../types/services/offerService.types";
import {IClientDocument} from "../../types/firestore/client_collection_types";
import moment from "moment";
import phoneNumberCountryCodeSanitizer from "../phoneNumberCountryCodeSanitizer";

export async function clientHandlerHelper(params: {
  organization?: string;
  organization_group?: string;
  cityGroup?: string,
  name: string,
  real_name?: string,
  phoneNumber: string,
  batch: firestore.WriteBatch,
  messageContext: IQiscusReceivedMessageBody;
  autoReply?: boolean;
  offerData?: IGetOffer['data'];
  vehicle?: IClientDocument['dream_vehicle'];
  notes?: string;
}, sessionType: ISessionMessageRef) {
  let {autoReply, phoneNumber, real_name, name, batch, messageContext, offerData} = params;
  let isNewUser: boolean = false;
  let clientRef!: firestore.DocumentReference;
  let finalName: string | null = real_name || name;

  let findClient = await firestoreService.clientCollection
    .where('contacts.whatsapp', "==", phoneNumber)
    .get();

  let amartahondaUrlParse = autoReply ? false : await parseUrlAmartaHonda({messageContext});

  const urlParse = amartahondaUrlParse as IParseUrlAmartaHonda;

  let cityGroup: null | {
    text: string;
    value: string
  } = null;
  let vehicleDream: any = null;

  if (params.cityGroup) {
    cityGroup = {
      text: params.cityGroup.toUpperCase(),
      value: params.cityGroup.toUpperCase(),
    }
  } else if (offerData?.area) {
    cityGroup = {
      text: offerData.area.toUpperCase(),
      value: offerData.area.toUpperCase(),
    }
  } else if (amartahondaUrlParse && urlParse.city) {
    cityGroup = {
      text: urlParse.city,
      value: urlParse.city,
    }
  }

  if (amartahondaUrlParse && urlParse.variant) {
    vehicleDream = {
      model_name: urlParse.variant.model_market_name.toUpperCase(),
      variant_code: urlParse.variant.code,
      variant_name: urlParse.variant.variant_name,
      color_name: null,
      color_code: null,
      year: null,
    }
  } else if (params.vehicle) {
    vehicleDream = {
      ...params.vehicle,
    }
  } else if (offerData?.vehicle) {
    const vehicle = offerData?.vehicle;
    vehicleDream = {
      model_name: vehicle?.model_name.toUpperCase(),
      variant_code: vehicle?.variant_code,
      variant_name: vehicle?.variant_name,
      color_name: vehicle?.color_name,
      color_code: vehicle?.color_code,
      year: vehicle?.year,
      buy_time: null,
    }
  }

  let isDeveloper = false;

  if (!findClient.empty) {
    // Assuming there's only one client per phone number. If not, this takes the first one.
    const clientDoc = findClient.docs[0];
    clientRef = clientDoc.ref;
    const data = clientDoc.data() as IClientDocument;

    isDeveloper = data.developer ?? false;

    finalName = params.real_name || data.profile.name || data.profile.temporary_name || null;

    const toUpdate: { [key: string]: any } = {};

    if (params.real_name) {
      toUpdate["profile.name"] = params.real_name;
    }

    if (offerData) {
      const orderHistory = {
        offer_code: offerData.transaction_code,
        created_at: moment(offerData.transaction_time).toDate(),
        payment_scheme: offerData.purchase_method.toUpperCase() as any,
        source: "OTHER"
      };

      const existingHistories = data.order_histories || [];
      const offerExists = existingHistories.some(o => o.offer_code === offerData?.transaction_code);

      if (!offerExists) {
        toUpdate["order_histories"] = [...existingHistories, orderHistory];
      }

      if (params.organization) {
        toUpdate["profile.organization"] = params.organization;
      }

      if (params.organization_group) {
        toUpdate["profile.organization_group"] = params.organization_group;
      }

      if (offerData.purchase_method === "credit") {
        const credit = offerData.credit;
        toUpdate["survey"] = {
          credit_scheme: {
            down_payment: credit.dp_amount,
            tenor: credit.tenor,
            installment: credit.installment_amount,
            down_payment_discount: 0,
          },
          offer_code: offerData.transaction_code,
          last_send: moment(offerData.transaction_time).toDate(),
        };
      } else if (offerData.purchase_method === "cash") {
        toUpdate["cash_offer"] = {
          offer_code: offerData.transaction_code,
          last_request_at: moment(offerData.transaction_time).toDate(),
        };
      }
    }

    if (cityGroup) {
      toUpdate["profile.area"] = cityGroup;
    }
    if (vehicleDream) {
      toUpdate["dream_vehicle"] = vehicleDream;
    }
    if (params.notes) {
      toUpdate["notes"] = {
        text: params.notes,
        updatedAt: firestore.Timestamp.now(),
      };
    }

    if (Object.keys(toUpdate).length > 0) {
      batch.update(clientRef, toUpdate);
    }
  } else {
    isNewUser = true;
    sessionType.sessionType = TSessionOfUser.NEW_USER;

    let insertData: IClientDocument = {
      notes: params.notes ? {
        text: params.notes,
        updatedAt: firestore.Timestamp.now(),
      } : null,
      contacts: {
        whatsapp: phoneNumber,
        phoneNumber: phoneNumber,
      },
      details: {
        order_maker_phone_number:
          offerData?.contact.phone_number_order_maker ?
            phoneNumberCountryCodeSanitizer(offerData.contact.phone_number_order_maker, "62", "62") : phoneNumber,
        owner_phone_number:
          offerData?.contact.phone_number_owner ?
            phoneNumberCountryCodeSanitizer(offerData.contact.phone_number_owner, "62", "62") : phoneNumber,
        guarantor_phone_number:
          offerData?.contact.phone_number_guarantor ?
            phoneNumberCountryCodeSanitizer(offerData.contact.phone_number_guarantor, "62", "62") : phoneNumber,

      },
      created_time: firestore.Timestamp.now(),
      profile: {
        email: null,
        name: params.real_name || null,
        temporary_name: params.name,
        phone_number: phoneNumber,
        area: cityGroup || null,
        organization: params.organization || null,
        organization_group: params.organization_group || null,
      },
      dream_vehicle: vehicleDream || null,
      leads: null,
      cash_offer: null,
      freeLeadsStatus: null,
      survey: null,
      order_histories: []
    }

    if (offerData) {
      insertData.order_histories.push({
        offer_code: offerData.transaction_code,
        created_at: moment(offerData.transaction_time).toDate(),
        payment_scheme: offerData.purchase_method.toUpperCase() as any,
        source: "OTHER"
      })

      if (offerData.purchase_method === "credit") {
        const credit = offerData.credit;
        insertData.survey = {
          credit_scheme: {
            down_payment: credit.dp_amount,
            tenor: credit.tenor,
            installment: credit.installment_amount,
            down_payment_discount: 0,
          },
          offer_code: offerData.transaction_code,
          last_send: moment(offerData.transaction_time).toDate(),
        }
      } else if (offerData.purchase_method === "cash") {
        insertData.cash_offer = {
          offer_code: offerData.transaction_code,
          last_request_at: moment(offerData.transaction_time).toDate(),
        }
      }

    }

    clientRef = await firestoreService.addNewClient(insertData, batch);
  }

  if (messageContext.referral) {
    batch.update(clientRef, {
      "leads.adSourceId": messageContext.referral.source_id || "",
    })
  }

  return {
    clientRef,
    isNewUser,
    city: cityGroup ? cityGroup.text : null,
    name: finalName,
    idAgen: urlParse ? urlParse.idAgen ?? null : null,
    isDeveloper,
  }
}

interface IParseUrlAmartaHonda {
  city: string | null;
  variant: IVariant | null;
  idAgen: null | string;
}

async function parseUrlAmartaHonda(params: {
  messageContext: IQiscusReceivedMessageBody;
}): Promise<IParseUrlAmartaHonda> {
  let {messageContext} = params;
  const text = textInMessageContext({messageContext});

  const regex = new RegExp(
    /((([A-Za-z]{3,9}:(?:\/\/)?)(?:[-;:&=+$,\w]+@)?[A-Za-z0-9.-]+|(?:www.|[-;:&=+$,\w]+@)[A-Za-z0-9.-]+)((?:\/[+~%\/.\w-_]*)?\??[-+=&;%@.\w_]*#?[.!\/\\w]*)?)/,
    "gi"
  );

  const findAll = text.match(regex);
  let result: IParseUrlAmartaHonda = {
    city: null,
    variant: null,
    idAgen: null,
  }

  if (findAll && findAll.length > 0) {

    for (const urlString of findAll) {
      const url = new URL(urlString);
      if (url.hostname === "amartahonda.com") {
        const segments = url
          .pathname
          .split('/')
          .filter(value => !!value);

        if (segments[0].toLowerCase() == "baru") {
          let variantCode = segments[2];
          variantCode = variantCode.slice(0, -1) + "/" + variantCode.slice(-1);

          const getVehicle = await autotrimitraServices.getVehicleVariantModel({
            code: variantCode,
          });
          if (getVehicle.data.data) {
            result.variant = getVehicle.data.data[0];
          }

          const citySegment = segments?.[1] ?? null;

          if (citySegment) {
            const fetchCity = await catalogueServices.getAvailableArea()
            const cityCollection = collect(fetchCity.data?.data ?? []);
            const group = cityCollection.groupBy('city_group');
            const keys = group.keys();
            const indexOfTarget = keys.toArray().indexOf(citySegment.toUpperCase());
            if (indexOfTarget) result.city = citySegment.toUpperCase();
          }
        } else if (segments[0].toLowerCase() === "wa") {
          result.idAgen = segments[1];
        }


        break;
      }
    }
  }

  return result;
}
