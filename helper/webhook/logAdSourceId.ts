import {myFirestore} from "../../services/google/firebaseAdmin";
import {firestore} from "firebase-admin";
import {IConversationFlowFirestoreDoc} from "../../types/conversationFlow/ver4/conversationFlow.types";
import moment from "moment";
import {TSessionOfUser} from "../../types/webhook/qiscuss";

interface Params {
    sourceId: string;
    phoneNumber: string;
    sessionType: TSessionOfUser | null;
    projectRef: firestore.DocumentReference;
}

interface LogDocument {
    sourceId: string;
    dealCode: string;
    phoneNumber: string;
    createdAt: firestore.Timestamp;
    successSendToAggregateEndpoint: boolean;
    isNewUser: boolean;
}

const logAdSourceId = async (params: Params) => {
    const startOfDay = moment().startOf("day");
    const endOfDay = moment().endOf("day");

    const logCollection = myFirestore.collection("ad_source_id_logs");
    const collectionConversationFlow = params.projectRef.collection("conversation_flow")

    const findBindDealCode = await collectionConversationFlow
        .where("referralSourceId.sourceId", "==", params.sourceId)
        .get()

    if (findBindDealCode.empty) return;

    const findSameDay = await logCollection
        .where("createdAt", ">=", startOfDay.toDate())
        .where("createdAt", "<=", endOfDay.toDate())
        .where("phoneNumber", "==", params.phoneNumber)
        .get();


    let doc = logCollection.doc();

    if (!findSameDay.empty) {
        return;
    }

    let data!: IConversationFlowFirestoreDoc;

    findBindDealCode.forEach(result => {
        data = result.data() as any;
    })

    const document: LogDocument = {
        sourceId: params.sourceId,
        phoneNumber: params.phoneNumber,
        dealCode: data.referralSourceId.dealCode,
        createdAt: firestore.Timestamp.now(),
        successSendToAggregateEndpoint: false,
        isNewUser: params.sessionType === TSessionOfUser.NEW_USER,
    }


    await doc.set(document);
}

export default logAdSourceId;
