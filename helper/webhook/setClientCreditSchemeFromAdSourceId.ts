import {firestore} from "firebase-admin";
import {myFirestore} from "../../services/google/firebaseAdmin";
import {IConversationFlowFirestoreDoc} from "../../types/conversationFlow/ver4/conversationFlow.types";
import conversationFlowGetDataDeal from "../../autoReplyFlow/ver3/conversationFlowGetDataDeal";
import {catalogueServices} from "../../services/catalogueServices";
import moment from "moment";

interface Params {
    clientRef: firestore.DocumentReference
    adSourceId: string;
    projectRef: firestore.DocumentReference;
}

const setClientCreditSchemeFromAdSourceId = async (
    params: Params,
) => {
    const logCollection = myFirestore.collection("ad_source_id_logs");
    const collectionConversationFlow = params.projectRef.collection("conversation_flow")

    const findBindDealCode = await collectionConversationFlow
        .where("referralSourceId.sourceId", "==", params.adSourceId)
        .get()

    if (findBindDealCode.empty) return;

    let doc = logCollection.doc();
    let data!: IConversationFlowFirestoreDoc;

    findBindDealCode.forEach(result => {
        data = result.data() as any;
    })

    if(!data.referralSourceId.dealCode) return;

    const dataDealCode = await conversationFlowGetDataDeal(data.referralSourceId.dealCode);
    const getVehicle = await catalogueServices.getVariantByCityGroup({
        area: dataDealCode.cityGroup,
        variantCode: dataDealCode.variantCode,
    })

    const vehicle = getVehicle?.data[0]

    if(!vehicle) return;

    await params.clientRef.update({
        dream_vehicle: {
            color_code: dataDealCode.originalResponse.vehicle.variant_custom[0].variant_color_code,
            color_name: dataDealCode.originalResponse.vehicle.variant_custom[0].variant_color_name,
            model_name: dataDealCode.originalResponse.vehicle.model_name,
            variant_code: dataDealCode.originalResponse.vehicle.variant_custom[0].variant_code,
            variant_name: dataDealCode.originalResponse.vehicle.variant_custom[0].variant_name,
            year: moment().year(),
            price: vehicle.price,
        },
        "survey.credit_scheme": {
            dealCode: dataDealCode.dealCode,
            discountInstallment: 0,
            discountTenor: 0,
            down_payment: dataDealCode.originalResponse.credit[0].dp_amount,
            installment: dataDealCode.originalResponse.credit[0].installment_amount,
            tenor: parseInt(dataDealCode.originalResponse.credit[0].tenor[0] as any),
            selectedLeasingCode: dataDealCode.originalResponse.credit[0].finco_code,
            lastUpdate: moment().toDate(),
            leasingAdminId: dataDealCode.originalResponse.default_finco_id,
            priceListSource: "dealCode"
        },
        "profile.area": {
            text: dataDealCode.cityGroup.toUpperCase(),
            value: dataDealCode.cityGroup.toUpperCase(),
        }
    })
}


export default setClientCreditSchemeFromAdSourceId;
