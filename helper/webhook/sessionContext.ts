import { firestore } from "firebase-admin";
import {v4} from "uuid";
import {IFirestoreMessageEntity} from "../../types/firestore/message_types";
import moment from "moment";
import {updateSessionLog} from "../updateSessionLog";
import {ISessionMessageRef, TSessionOfUser} from "../../types/webhook/qiscuss";
import {IQiscusReceivedMessageBody} from "../../types/services/qiscus/received_message_types";

export async function sessionHandlerHelper(params: {
    roomRef: firestore.DocumentReference;

    messageId: string;

    name: string,
    phoneNumber: string,
    clientRef: firestore.DocumentReference;

    date: Date,
    messageContext: IQiscusReceivedMessageBody

    batch: firestore.WriteBatch,
    isFromSystem?: boolean,
}, sessionType: ISessionMessageRef) {
    let {
        isFromSystem,
        roomRef,

        name,
        phoneNumber,
        clientRef,

        date,

        messageContext,

        batch,
    } = params;

    let sessionId = v4();
    let isNewSession = false;
    const momentDateMessage = moment(date);

    const lastInboundMessage = await roomRef.collection('chats')
        .where('message.direction', '==', "IN")
        .orderBy('message.timestamp', 'desc').limit(1)
        .get();

    if (lastInboundMessage.size === 1) {
        lastInboundMessage.forEach(result => {
            const data = result.data() as IFirestoreMessageEntity;

            const momentLastInboundMessage = moment(data.message.timestamp.toDate());
            if (
                momentDateMessage
                    .isBefore(
                        momentLastInboundMessage.add(24, 'hours')
                    )
            ) {
                if (data.session_id) sessionId = data.session_id;
                sessionType.sessionType = TSessionOfUser.EXTEND_SESSION;
            } else {
                sessionType.sessionType = TSessionOfUser.NEW_SESSION;
                isNewSession = true;
            }
        });
    }

    const messageParams: IFirestoreMessageEntity = {
        message: {
            ...messageContext,
            direction: isFromSystem ? "OUT" : "IN",
            unixtime: date.getTime() / 1000,
            timestamp: firestore.Timestamp.fromDate(date),
        },
        statuses: {
            delivered: null,
            read: null,
            sent: null,
            failed: null,
            deleted: null,
        },
        origin: {
            display_name: isFromSystem ? "SYSTEM_REPLY" : name,
            id: isFromSystem ? null : phoneNumber,
            reference: isFromSystem ? null : clientRef,
            system_reply: isFromSystem || false,
        },
        session_id: sessionId,
    };

    await updateSessionLog({
        message: messageParams,
        chatRoomRef: roomRef,
        batch: batch,
        sessionId: sessionId,
        customerPhoneNumber: phoneNumber,
        clientRef: clientRef,
    })

    return {
        sessionId,
        isNewSession,
    }
}
