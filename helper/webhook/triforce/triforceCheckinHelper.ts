import {firestore} from "firebase-admin";
import idealServices from "../../../services/ideal_backend/idealServices";
import {AxiosError} from "axios";

const responseCheckin = [
  "Semangat pagi, terima kasih sudah check in!! Hari ini rencana penjualan nya apa?",
  "Semangat pagi, terima kasih sudah check in!! Ada kendala apa di lapangan?",
  "Semangat pagi, terima kasih sudah check in!! Sudah sesuai target rencana hari ini? Ada kendala?",
  "Semangat pagi, terima kasih sudah check in!! Bagaiamana Kondisi lapangan sejauh ini?",
  "Semangat pagi, terima kasih sudah check in!! Hari ini lancar atau ada kendala?"
]

const triforceCheckinHelper = async (
  params: {
    roomRef: firestore.DocumentReference;
    clientRef: firestore.DocumentReference;
    batch: firestore.WriteBatch;
    phoneNumber: string;
    messageId: string;
    checkin: {
      count: number;
    }
  }
) => {
  const text = responseCheckin[params.checkin.count - 1];
  if (!text) {
    return;
  }

  try {
    await idealServices.sendMessage({
      senderId: "SYSTEM",
      senderName: "ANGGA OWNER",
      roomPath: params.roomRef.path,
      phoneNumber: params.phoneNumber,
      text: text,
      replyMessageId: params.messageId,
    })
  } catch (e) {
    const error = e as AxiosError
    console.log(error.response?.data)
  }

}

export default triforceCheckinHelper;
