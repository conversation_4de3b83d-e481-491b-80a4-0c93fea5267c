import {firestore} from "firebase-admin";
import {firestoreService} from "../../services/google/FirestoreService";
import {trimitraAiAgentServices} from "../../services/trimitraAiAgent";
import {TrimitraAiMessage} from "../../types/services/trimitraAiAgent.types";

interface Params {
  chatRoomRef: firestore.DocumentReference;
  organization: string;
  replyMessageId?: string;
}

interface Response {
  success: boolean;
  response: string | null;
  error: any;
  fullResponse: any;
  replyMessageId: string | null;
}

const llmGenerateMessage = async (params: Params): Promise<Response> => {
  try {
    const getChats = await firestoreService.getChats(params.chatRoomRef);
    const getChatRoomData = await firestoreService.getChatRoomData(params.chatRoomRef);
    const getClient = await firestoreService.getClientFromChatRoom(params.chatRoomRef);

    // last chat
    const lastChat = getChats[getChats.length - 1];

    let text = "";

    if (lastChat.message.referral) {
      text += `<PERSON><PERSON> tertarik dengan iklan di bawah ini.\nID: ${lastChat.message.referral.source_id}\nHeadline: ${lastChat.message.referral.headline} \nBody: ${lastChat.message.referral.body}\n\n`;
    }

    switch (lastChat.message.type) {
      case "text":
        text += lastChat.message.text?.body ?? "";
        break;
      case "image":
        text += lastChat.message.image?.caption ?? "";
        break;
      case "video":
        text += lastChat.message.video?.caption ?? "";
        break;
      case "voice":
        text += lastChat.message.voice?.caption ?? "";
        break;
      case "document":
        text += lastChat.message.document?.caption ?? "";
        break;
    }

    text = text.trim()

    const message: TrimitraAiMessage = {
      text: text,
    }

    // Call Trimitra AI Agent
    const aiResponse = await trimitraAiAgentServices.chatIdeal({
      message: message,
      chatroom_path: params.chatRoomRef.path,
      real_name: getClient.profile.name || null,
      city_group: getChatRoomData.cityGroup || null,
      connected_to_admin: !getChatRoomData.agent_ai_reply,
      organization: params.organization,
    });

    return {
      success: true,
      response: aiResponse.data.response,
      error: null,
      fullResponse: aiResponse,
      replyMessageId: params.replyMessageId || null,
    };

  } catch (error) {
    console.error('Error in llmGenerateMessage:', error);
    return {
      success: false,
      response: null,
      error: error,
      fullResponse: null,
      replyMessageId: params.replyMessageId || null,
    };
  }
}

export default llmGenerateMessage;
