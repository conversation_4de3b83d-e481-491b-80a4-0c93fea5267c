import {firestore} from "firebase-admin";
import {messageContextHandlerHelper} from "./messageContext";
import {myFirestore} from "../../services/google/firebaseAdmin";
import moment from "moment";
import {qiscusServiceNew} from "../../services/QiscusServiceNew";
import {ISessionMessageRef, TSessionOfUser} from "../../types/webhook/qiscuss";
import {ProjectDocumentTypes} from "../../types/firestore/projectDocumentTypes";
import metaServices from "../../services/MetaServices";

const slowResponseMessageAmartaHonda = `<PERSON><PERSON>,
Terima kasih telah menghubungi Amarta Honda.

⚙️ *Jam Layanan Fast Response WA*: 07.00 – 21.00 WIB  
Mohon maaf, saat ini kami sedang offline. Sementara itu, Anda dapat melakukan:
✅ Pemesanan produk
✅ Cek status stok
Langsung melalui website resmi kami:
👉 https://amartahonda.com

<PERSON><PERSON>,
<PERSON>`;

const slowResponseMessageAmartaMobil = `<PERSON><PERSON>,
<PERSON><PERSON> kasih telah menghubungi Amarta Mobil.

⚙️ *Jam Layanan Fast Response WA*: 07.00 – 21.00 WIB  
Mohon maaf, saat ini tim kami belum dapat segera membalas pesan Anda. Untuk kemudahan Anda, silakan akses:
• https://amartachery.com  
• https://amartaneta.com  
• https://amartavinfast.com

Salam hangat,
Tim Amarta Mobil`;

const sendMessageSlowResponse = async (params: {
    chatRoomRef: firestore.DocumentReference;
    clientRef: firestore.DocumentReference;
    phoneNumber: string,
    session: ISessionMessageRef,
    projectRef: firestore.DocumentReference;
}) => {

    let message = slowResponseMessageAmartaHonda;
    if(params.projectRef.id === "kpsDdEcRtReOQrCYkzq2") {
        message = slowResponseMessageAmartaMobil;
    }

    const getProject = await params.projectRef.get();
    const projectData = getProject.data() as ProjectDocumentTypes

    if (
        params.session.sessionType &&
        [TSessionOfUser.NEW_USER, TSessionOfUser.NEW_SESSION].indexOf(params.session.sessionType) === -1
    ) {
        return;
    }

    const now = moment();
    const time07 = moment().set({hour: 7, minute: 0, second: 0});
    const time21 = moment().set({hour: 21, minute: 0, second: 0});

    if (now.isBetween(time07, time21)) {
        return;
    }
    let messageId: string = "";

    if(projectData.provider === "qiscus") {
        const sendMessage = await qiscusServiceNew.sendMessage({
            text: message,
            target: params.phoneNumber,
        })
        messageId = sendMessage.data.messages[0].id;
    } else if (projectData.provider === "meta") {
        const sendMessage = await metaServices.sendMessage({
            payload: {
                to: params.phoneNumber,
                type: "text",
                text: {
                    body: message,
                },
                recipient_type: "individual",
                preview_url: false,
            },
            meta: {
                bearerToken: projectData.meta?.bearer || "",
                phoneNumberId: projectData.meta?.phoneNumberId || "",
            }
        })

        messageId = sendMessage.data.messages[0].id;
    }

    const batch = myFirestore.batch();
    await messageContextHandlerHelper({
        roomRef: params.chatRoomRef,
        sessionId: "",
        messageId: messageId,
        name: "SYSTEM_REPLY",
        phoneNumber: params.phoneNumber,
        clientRef: params.clientRef,
        batch: batch,
        date: now.toDate(),
        messageContext: {
            id: messageId,
            type: "text",
            text: {
                body: message,
            },
            timestamp: (now.unix() / 1000).toString(),
            from: messageId,
        },
        bindDocuments: [],
    }, true);

    await batch.commit();
}

export default sendMessageSlowResponse;
