import {IFirestoreMessageEntity} from "../types/firestore/message_types";
import {myFirestore} from "../services/google/firebaseAdmin";
import {ISessionLog, ISessionLogDataClient} from "../types/firestore/session_log_types";
import {firestore} from "firebase-admin";

export async function updateSessionLog(params: {
    message: IFirestoreMessageEntity,
    sessionId: string,
    chatRoomRef: firestore.DocumentReference;
    batch?: firestore.WriteBatch;
    customerPhoneNumber: string;
    clientRef: firestore.DocumentReference;
}) {
    const clientSessionDoc = myFirestore.collection('client_session_logs').doc(params.sessionId);
    const getClientSessionDoc = await clientSessionDoc.get();
    const payload: ISessionLog<ISessionLogDataClient> = {
        sent_to_bigquery_at: null,
        data_analytic: null,
        messages: [],
        chat_room: params.chatRoomRef,
        client: params.chatRoomRef,
        last_inbound_at: params.message.message.timestamp,
        sessionStartAt: firestore.Timestamp.now(),
        auto_end_session_at: firestore.Timestamp.fromMillis(params.message.message.timestamp.toMillis() + 86400000),
        customerPhoneNumber: params.customerPhoneNumber,
        sessionId: clientSessionDoc.id,
        hasSentSlowRespMessage: false,
        ref: clientSessionDoc,
    }
    if (getClientSessionDoc.exists) {
        const data = getClientSessionDoc.data() as ISessionLog<ISessionLogDataClient>;
        payload.hasSentSlowRespMessage = data.hasSentSlowRespMessage || false;
        payload.sessionStartAt = data.messages[0].message.timestamp;
        payload.messages = [
            ...data.messages,
            {
                message: params.message.message,
                origin: params.message.origin,
            }
        ];
    } else {
        payload.messages.push({
            message: params.message.message,
            origin: params.message.origin,
        });
    }

    if (params.batch) {
        params.batch.set(clientSessionDoc, payload)
    } else {
        await clientSessionDoc.set(payload)
    }

    return {
        clientSessionDoc: clientSessionDoc,
        data: payload,
    };
}
