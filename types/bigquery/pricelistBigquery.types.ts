import {BigQueryTimestamp} from "@google-cloud/bigquery";

export interface IPriceListBigqueryCreditScheme {
    down_payment?: number | null;
    tenor?: number | null;
    installment?: number | null;
    discount_down_payment?: number | null;
    discount_tenor?: number | null;
    discount_installment?: number | null;
}

export interface IPriceListBigquery {
    event: string;
    uuid?: string | null;
    phone_number?: string | null;
    name?: string | null;
    city_group?: string | null;
    vehicle?: {
        model_name?: string | null;
        variant_name?: string | null;
        variant_code?: string | null;
        variant_color_code?: string | null;
        variant_color_name?: string | null;
    } | null;
    discount_promo?: number | null;
    otr?: number | null;
    pricelists?: IPriceListBigqueryCreditScheme[] | null;
    admin_id?: string | null;
    created_at?: BigQueryTimestamp; // assuming TIMESTAMP is represented as string
    credit?: {
        offer_code?: string | null;
        down_payment?: number | null;
        discount_down_payment?: number | null;
        tenor?: number | null;
        discount_tenor?: number | null;
        installment?: number | null;
        discount_installment?: number | null;
    } | null;
}
