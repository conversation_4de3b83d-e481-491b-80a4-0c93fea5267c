import {
    IInteractiveInboundContext,
    IPostMessageContext,
    TAvailablePostMessageContext
} from "../services/qiscus/qiscus_message_context_types";
import {INotification} from "../services/qiscus/notification_message_types";
import {IReferralMessageContext} from "../services/qiscus/received_message_types";

export interface MetaWebhookNotificationBase {
    object: string;
    entry: Entry[];
}

export interface Entry {
    id: string;
    changes: Change[];
}

export interface Change {
    value: Value;
    field: string;
}

export interface Value {
    messaging_product: string;
    metadata: Metadata;
    contacts: Contact[];
    messages: Message[];
    statuses: INotification[]
}

export interface Contact {
    profile: Profile;
    wa_id: string;
}

export interface Profile {
    name: string;
}

export interface Message extends Omit<IPostMessageContext, "interactive"> {
    from: string;
    id: string;
    timestamp: string;
    "type": TAvailablePostMessageContext;
    "interactive": IInteractiveInboundContext;
    "referral": IReferralMessageContext;
}

export interface Metadata {
    display_phone_number: string;
    phone_number_id: string;
}
