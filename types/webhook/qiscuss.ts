export enum TSessionOfUser {
    NEW_USER,
    EXTEND_SESSION,
    NEW_SESSION,
}

export interface ISessionMessageRef {
    sessionType: null | TSessionOfUser;
}

export const postMessage = {
    newUser: () => {
        return 'Selamat datang di WhatsApp Resmi amartahonda.com...\n\n' +
            'Akun kami sudah terverifikasi dan bisa dilihat dari tanda ✅\n\n' +
            'Tips membeli Sepeda Motor Honda Praktis & Aman!🛵\n\n' +
            '1. Hindari penipuan dengan membayar pada saat motor diterima.\n\n' +
            '2. Inden atau booking tidak dipungut biaya apapun \n\n' +
            '3. Harga OTR adalah harga termasuk kepengurusan surat-surat kendaraan termasuk STCK/Ompang/Plat Sementara\n\n' +
            'Mohon membantu kami dengan menyebutkan nama, kota/kab domisili, dan perihal yang bisa kami bantu.'
    },
}

export interface QiscussWebhook {
    payload: {
        from: {
            email: string;
            name: string;
        },
        message: {
            created_at: Date;
            payload: {
                caption: string,
                url: string,
            },
            text: string,
            timestamp: Date;
            type: string;
            unique_temp_id: string;
        },
        room: {
            id: number;
            id_str: string;
            name: string;
            participants: { email: string }[];
        },
    }
}
