export interface CatalogueBaseResponse<T> {
    data: T;
}

export interface CatalogueCity {
    name: string;
    code: string;
}

export interface Area {
    area: string;
    city_name: string;
    city_code: string;
    city_group: string;
}

export interface Model {
    model_name: string;
    class: string;
    price: number;
    price_max: number;
    price_min: number;
    url_image: string;
    last_update: string;
}

export interface VariantCatalogue {
    model_name: string;
    class: string;
    price: number;
    year_production: null;
    variant_code: string;
    variant_name: string;
    price_max: number;
    price_min: number;
    url_image: string;
    last_update: string;
}

export interface VariantProduct {
    variant_code: string,
    variant_name: string,
    color_code: string,
    color_name: string,
    model_name: string,
    class: string,
    price: number,
    price_strikethrough: number,
    url_image: string,
    url_video: string,
    url_brochure: string,
    url_specification: string,
    last_update: string,
    active: boolean;
    registration_year?: string;
    license_plate?: string;
}

export interface MediaDetailItem {
    caption: string,
    description: "",
    url_thumbnail: string,
    url: string
}

export interface DetailProduct {
    entity_type: string,
    variant_code: string,
    variant_name: string,
    model_category: string,
    model_class: string,
    model_name: string,
    class: string,
    detail_image: MediaDetailItem[],
    "detail_social_media": [],
    "detail_video": MediaDetailItem[],
    "detail_document": MediaDetailItem[];
    "active": true
}


export type GetAvailableAreaResponse = CatalogueBaseResponse<Area[]>;
export type GetDetailProductResponse = CatalogueBaseResponse<DetailProduct[]>;
export type GetAvailableModelResponse = CatalogueBaseResponse<Model[]>;
export type GetAvailableVariant = CatalogueBaseResponse<VariantCatalogue[]>;
