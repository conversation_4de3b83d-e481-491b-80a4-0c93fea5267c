export interface ArtItem {
	art_class: string;
	art_code: string;
	brand_name: string;
	brand_uid: string;
	art_url: string;
	art_url_thumbnail: string;
	image_url: string;
	image_url_thumbnail: string;
	created_time: string;
}

export interface Pagination {
	total: number;
	start: number;
	length: number;
	current_page: number;
	total_pages: number;
}

export interface GetFeedResponse {
	success: boolean;
	data: {
		caption: string;
		latest_update_time: string;
		data: ArtItem[];
		pagination: Pagination;
	};
	meta: null;
}

export interface ArtAssets {
	type: string;
	url_asset_thumbnail: string;
	url_asset: string;
}

export interface GetMediaByArtCodeResponse {
	data: {
		art_code: string;
		art_class: string;
		art_category: string;
		art_assets: ArtAssets;
		art_note: string;
		owner: string;
		owner_name: string;
		transaction_time: string;
		used_status: string;
		created_time: string;
		product_brand: string;
		product_model: string;
		product_variant_code: string;
		product_variant_name: string;
		product_variant_color_code: string;
		product_variant_color_name: string;
		product_category_uuid: string;
		product_brand_uuid: string;
		product_model_uuid: string;
		product_variant_uuid: string;
		product_variantColor_uuid: string;
		distributed_count: number;
		distributing_count: number;
		total_reward: number;
		total_comment: number;
		meta_data: {
			media_id: string;
			media_expired_date: string;
			project_name: string;
			project_id: string;
		} | null;
	};
}

export interface FeedEventMetaData {
	platform_target: string;
	platform_user_id: string | null;
	platform_url: string | null;
	platform_caption: string;
	user_agent: string | null;
	user_ip: string | null;
	referrer: string | null;
}

export interface FeedEventRequest {
	source: string;
	event: string;
	art_class: string;
	art_code: string;
	user_id: string;
	notes: string;
	meta_data: FeedEventMetaData;
}
