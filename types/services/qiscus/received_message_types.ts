import {
    IInteractiveInboundContext,
    IPostMessageContext,
    TAvailablePostMessageContext
} from "./qiscus_message_context_types";
import {IBaseQiscusReceivedNotification} from "./notification_message_types";

export interface IBaseQiscusReceivedMessage {
    "contacts": IQiscusReceivedMessageContact[],
    "messages": IQiscusReceivedMessageBody[]
}

interface IQiscusReceivedMessageContact {
    "profile": {
        "name": string;
    },
    "wa_id": string;
}

export interface IQiscusReceivedMessageBody extends Omit<IPostMessageContext, "interactive"> {
    "from": string,
    "id": string,
    "timestamp": string,
    "type": TAvailablePostMessageContext;
    "interactive"?: IInteractiveInboundContext;
    "referral"?: IReferralMessageContext;
}

export interface IReferralMessageContext {
    source_id: string;
    media_type: string;
    body: string;
    source_url: string;
    video_url: string;
    thumbnail_url: string;
    headline: string;
    source_type: string;
}


export interface INewBaseQiscusReceivedMessage {
    entry: {
        changes: {
            value: {
                whatsapp_business_api_data: IBaseQiscusReceivedMessage | IBaseQiscusReceivedNotification;
            }
        }[];
        id: string;
    }[]
}
