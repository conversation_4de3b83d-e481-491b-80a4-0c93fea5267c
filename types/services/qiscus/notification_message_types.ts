export interface IBaseQiscusReceivedNotification {
    "statuses": INotification[]
}

export interface INotification {
    "id": string,
    "recipient_id": string,
    "status": TMessageStatus,
    "timestamp": string,
    "type": "message",
    "conversation"?: {
        "id": string,
        "expiration_timestamp": number,
        "origin": {
            "type": string
        }
    },
    "pricing"?: {
        "pricing_model": string,
        "billable": boolean,
        "category": string;
    };
    errors?: [
        {
            code: number,
            href: string,
            title: string
        }
    ],
}

export type TMessageStatus = "sent" | "delivered" | "read" | "failed" | "deleted";

