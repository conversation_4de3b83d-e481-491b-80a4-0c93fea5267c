import {IPostMessageContext, TAvailablePostMessageContext} from "./qiscus_message_context_types";

export interface IQiscusSendMessageParams {
    target: string;
    text?: string;
    imageUrl?: string;
}

export interface IPayloadQiscusPostMessage extends IPostMessageContext {
    to: string,
    recipient_type: "individual" | "group",
    type: TAvailablePostMessageContext ,
    preview_url?: boolean,
}