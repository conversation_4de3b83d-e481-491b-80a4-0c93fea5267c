export interface IPostMessageContext {
    audio?: IFileContextWebhook;
    document?: IFileContextWebhook;
    image?: IFileContextWebhook;
    video?: IFileContextWebhook;
    voice?: IFileContextWebhook;
    system?: ITextContextWebhook;
    text?: ITextContextWebhook;
    context?: IReplyContext;
    sticker?: IStickerContext;
    locations?: ILocationContextWebhook;
    interactive?: IInteractiveContextSendMessage;
    button?: IButtonContext;
}

export type TAvailablePostMessageContext =
    "audio"
    | "document"
    | "image"
    | "location"
    | "system"
    | "text"
    | "video"
    | "voice"
    | "context"
    | "contacts"
    | "sticker"
    | "button"
    | "referral"
    | "interactive";

export interface IFileContextWebhook {
    id?: string,
    link?: string,
    caption: string
}

export interface ILocationContextWebhook {
    address: string,
    latitude: number,
    longitude: number,
    name: string
}

export interface ITextContextWebhook {
    body: string;
}

export interface IStickerContext {
    id: string;
    metadata: {
        emojis: string[];
        "is-first-party-sticker": number;
        "sticker-pack-id": string;
    };
    "mime_type": string,
    "sha256": string,
}

export interface IReplyContext {
    from: string;
    id: string;
    mentions: string[]
}

export interface IInteractiveContextSendMessage {
    type: "list" | "button";
    header?: {
        type: "text" | "video" | "image" | "document",
        text?: string;
        video?: IFileContextWebhook;
        image?: IFileContextWebhook;
        document?: IFileContextWebhook;
    };
    body: {
        text: string;
    };
    footer?: {
        text: string;
    };
    action: {
        button?: string;
        buttons?: {
            type: "reply";
            reply?: {
                title: string;
                id: string;
            }
        }[]

        sections?: {
            title: string;
            rows: {
                id: string;
                title: string;
                description?: string;
            }[];
        }[]
    };
}

export interface IInteractiveInboundContext {
    type: "list_reply" | 'button_reply';
    list_reply?: {
        id: string;
        title: string;
        description: string;
    };
    button_reply?: {
        id: string;
        title: string;
    }
}

export interface IQiscusResponsePostMessage {
    "messages": {
        "id": string
    }[],
    "meta": {
        "api_status": "stable",
        "version": "2.29.3"
    }
}


export interface IButtonContext {
    payload: string;
    text: string;
}
