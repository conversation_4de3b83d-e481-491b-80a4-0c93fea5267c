export interface GetTemplateResponseMeta {
    data: Template[];
    paging: Paging;
}

interface Template {
    name: string;
    components: Component[];
    language: string;
    status: string;
    category: string;
    id: string;
}

interface Component {
    type: string;
    format?: string;
    text?: string;
    example?: Example;
    buttons?: Button[];
}

interface Example {
    body_text: string[][];
}

interface Button {
    type: string;
    text: string;
}

interface Paging {
    cursors: Cursors;
}

interface Cursors {
    before: string;
    after: string;
}
