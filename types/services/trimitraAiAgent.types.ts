export interface TrimitraAiMessage {
  text: string;
}

export interface TrimitraAiChatRequest {
  message: TrimitraAiMessage;
  chatroom_path: string;
  real_name: string | null;
  city_group: string | null;
  connected_to_admin: boolean | null;
  organization: string | null;
}

export interface TrimitraAiChatResponse {
  status_code: number;
  success: boolean;
  message: string;
  data: {
    response: string;
  };
  metadata: any;
}

export interface TrimitraAiError {
  status_code: number;
  success: false;
  message: string;
  data?: any;
  metadata?: any;
}
