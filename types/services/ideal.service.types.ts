export interface AddLeadReqBody {
	agentCode: string;
	agentName: string;
	title: string;
	firstName: string;
	lastName: string;
	phoneNumber: string;
	email: string;
	provinceName: string;
	provinceCode: string;
	cityName: string;
	cityCode: string;
	vehicleUsage: "individual" | "shared" | "corporate";
	paymentPlan: "cash" | "credit";
	hasVehicleLoan: boolean;
	vehicleOptions: {
		brand: {
			name: string;
		};
		model: {
			name: string;
		};
		variant: {
			code: string;
			name: string;
		};
		color: {
			code: string;
			name: string;
		};
	}[];
	source: string;
	source2: string;
	purchasePlan: "firstVehicle" | "vehicleReplacement" | "vehicleAddition";
	nextTotalVehicleOwnerShip: string;
	notes: string;

	idCard_number: string | null;
	driverLicense_number: string | null;
}
