import { firestore } from "firebase-admin";

export type IConversationFlowStartAt =
	| "leadsCredit"
	| "referralSourceId"
	| "newCustomer";
export type IConversationFlowMessageType =
	| "text"
	| "priceList"
	| "button"
	| "internalTemplate"
	| "mediaTrimitraArt";
export type IConversationFlowMessageOnCustomerReplied = "saveName";

export interface IConversationFlow {
	topicName: string;
	startAt: IConversationFlowStartAt;
	messages: IConversationFlowMessage[];
}

export interface IConversationFlowMessage {
	uuid: string;
	parent: string | null;
	type: IConversationFlowMessageType | null;
	text: {
		body: string;
	};
	priceList: {
		text: string;
	};
	button: {
		text: string;
		buttons: { id: string; text: string }[];
	};
	internalTemplate: {
		templateId: string;
	};
	mediaTrimitraArt: {
		artCode: string;
		text: string;
	};
	onReplied: IConversationFlowMessageOnCustomerReplied | null;
}

export interface IConversationFlowFirestoreDoc<TIMESTAMP = firestore.Timestamp>
	extends IConversationFlow {
	updatedAt: TIMESTAMP;
	createdAt: TIMESTAMP;
	referralSourceId: {
		dealCode: string;
		sourceId: string;
	};
}
