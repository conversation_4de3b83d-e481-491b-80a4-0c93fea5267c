import { firestore } from "firebase-admin";
import {IFirestoreMessageEntity} from "./message_types";

export interface ISessionLog<T> {
    sent_to_bigquery_at: firestore.Timestamp | Date | null;
    data_analytic: T | null;
    messages: Pick<IFirestoreMessageEntity, "message" | "origin">[];
    chat_room: firestore.DocumentReference;
    client: firestore.DocumentReference;
    last_inbound_at: firestore.Timestamp;
    auto_end_session_at: firestore.Timestamp;
    customerPhoneNumber?: string;
    sessionId: string;
    ref: firestore.DocumentReference;
    hasSentSlowRespMessage: boolean;
    sessionStartAt: firestore.Timestamp;
}

export interface ISessionLogDataClient<DATE = firestore.Timestamp | Date> {
    uuid: string;
    client_id: string;
    session_start: DATE;
    session_end: DATE;
    inbound_text: number;
    outbound_text: number;
    list_inbound_link: string[];
    inbound_img: number;
    outbound_img: number;
    avg_inbound_txt_chars: number;
    avg_outbound_txt_chars: number;
    first_time_resp: number;
    min_time_resp: number;
    max_time_resp: number;
    avg_time_resp: number;
    inbound_link: number;
    outbound_link: number;
    created_at: DATE;
    messages: { direction: "IN" | "OUT"; ref_path: string }[];
    department: string | null;
    label: string | null;
    model: {
        name: string;
    } | null;
    variant: {
        name: string;
        code: string;
    } | null;
    color: {
        name: string;
        code: string;
    } | null;
    inbound: number;
    outbound: number;
    provider: string;
    
    projectName: string;
    projectId: string;
    projectPhoneNumber: string;
}

export interface IAdminSessionLogAnalyticData<DATE = firestore.Timestamp | Date> {
    uuid: string;
    agent_uid: string;
    agent_name: string;
    agent_email: string;
    session_start: DATE;
    session_end: DATE;
    session_duration: number;
    outbound_text: number;
    outbound_img: number;
    outbound_link: number;
    created_at: DATE;
    messages: { direction: "IN" | "OUT"; ref_path: string }[];
}

export interface IAdminSessionLog {
    last_heartbeat: firestore.Timestamp;
    auto_end_session_at: firestore.Timestamp;
    signed_in_at: firestore.Timestamp;
    admin: {
        name: string;
        email: string;
        ref: firestore.DocumentReference;
    };
    data_analytic: IAdminSessionLogAnalyticData | null;
    messages: (Pick<IFirestoreMessageEntity, "message" | "origin"> & { ref: firestore.DocumentReference })[];
    send_to_bigquery_at: firestore.Timestamp | Date | null;
}
