import {firestore} from "firebase-admin";
import {TMessageStatus} from "../services/qiscus/notification_message_types";
import {TAvailablePostMessageContext} from "../services/qiscus/qiscus_message_context_types";

export interface IChatRoomDocument {
    contacts: string[],
    clients: firestore.DocumentReference[],
    doc_department: firestore.DocumentReference | null;
    label: firestore.DocumentReference | null;
    label_updated_at: firestore.Timestamp | null;
    headers: {
        title: string
    },
    recent_chat: {
        contact: string,
        statuses: TStatuses,
        type: TAvailablePostMessageContext,
        direction: "IN" | "OUT",
        display_name: string,
        unixtime: number,
        timestamp: firestore.Timestamp,
        text: string
    };

    wait_for_answer?: {
        asked_at: firestore.Timestamp;
        question_id: string;
        topic: string;
    } | null;
    conversation_flow_executed?: boolean;

    last_inbound: firestore.Timestamp;
    last_message_type: "IN" | "OUT";
    organization: string | null;
    organizationUpdatedBy: string | null;
    organizationUpdatedAt: firestore.Timestamp | null;
    organizationGroup: string | null;
    cityGroup: string | null;
    cityGroupUpdatedAt: firestore.Timestamp | null;
    cityGroupUpdatedBy: string | null;
    blockReason: null | string;
    blocked: boolean;
    created_at: firestore.Timestamp;
    dream_vehicle: {
        buy_time: firestore.Timestamp | null;
        color_code: string | null;
        color_name: string | null;
        model_name: string | null;
        variant_code: string | null;
        variant_name: string | null;
        year: string | null;
    } | null;
    agent_ai_reply: boolean;
}

export type TStatuses = Record<TMessageStatus, firestore.Timestamp | null>;
