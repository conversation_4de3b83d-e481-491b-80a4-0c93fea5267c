import { firestore } from "firebase-admin";

export interface IAgentRefCode {
	agentCode: string;
	agentName: string;
	phoneNumber: string;
	fullName: string;
	province: {
		code: string;
		name: string;
	};
	city: {
		code: string;
		name: string;
	};
	vehicle: {
		brand: {
			name: string;
		};
		model: {
			name: string;
		};
		variant: {
			code: string;
			name: string;
		};
		color: {
			code: string;
			name: string;
		};
	};
	notes: string | null;
	refCode: string;
	createdAt: firestore.Timestamp;
	updatedAt: firestore.Timestamp;
	organization: string;
	source: string;
	isLeadsSent: boolean;
}
