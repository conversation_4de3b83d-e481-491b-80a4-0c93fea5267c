import {firestore} from "firebase-admin";

export interface IClientDocument {
    developer?: boolean | null;
    notes?:  {
        text: string;
        updatedAt: firestore.Timestamp;
    } | null;
    contacts: {
        whatsapp: string;
        phoneNumber: string;
    },
    created_time: firestore.Timestamp | Date;
    profile: {
        name: string | null;
        phone_number: string;
        area?: { text: string; value: string } | null;
        email: null | string;
        organization?: string | null;
        organization_group?: string | null;
        temporary_name?: string | null;
    }
    dream_vehicle: {
        model_name: string;
        variant_code: string;
        variant_name: string;
        color_name?: string | null;
        color_code?: string | null;
        year?: string | null;
        buy_time: firestore.Timestamp | Date | null;
    } | null;
    details: {
        owner_phone_number: string | null;
        order_maker_phone_number: string | null;
        guarantor_phone_number: string | null;
    };
    cash_offer: {
        offer_code: string;
        last_request_at: firestore.Timestamp | Date;
    } | null
    survey: {
        last_send: firestore.Timestamp | Date | null;
        offer_code: string | null;
        credit_scheme: {
            down_payment: number;
            down_payment_discount: number;
            installment: number;
            tenor: number;
        } | null;
    } | null;

    freeLeadsStatus: {
        acquired?: boolean;
        agentCode?: string;
        agentName?: string;
        acquiredAt?: firestore.Timestamp | Date | null;
        submitted: boolean;
        createdAt: firestore.Timestamp | Date | null;
    } | null

    order_histories: IClientEntityOrderHistory[];
    leads: {
        [key: string]: any;
    } | null;
}

interface IClientEntityOrderHistory {
    offer_code: string;
    created_at: firestore.Timestamp | Date;
    payment_scheme: "CASH" | "CREDIT",
    source: "IDEAL" | "TRIFORCE" | "OTHER",
}
