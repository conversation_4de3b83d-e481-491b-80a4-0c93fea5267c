import { firestore } from "firebase-admin";
import {TStatuses} from "./chat_room_document_types";
import {
    IFileContextWebhook,
    IInteractiveContextSendMessage,
    IInteractiveInboundContext,
    ILocationContextWebhook,
    IReplyContext,
    IStickerContext,
    ITextContextWebhook,
    TAvailablePostMessageContext
} from "../services/qiscus/qiscus_message_context_types";
import {IReferralMessageContext} from "../services/qiscus/received_message_types";

export interface IFirestoreMessageEntity {
    isTemplate?: boolean;
    template?: {
        name: string;
        components: any[];
    };
    origin: {
        id: string | null,
        display_name: string | null,
        reference: firestore.DocumentReference | null,
        system_reply: boolean;
    },
    message: {
        id: string,
        audio?: IFileContextWebhook;
        document?: IFileContextWebhook;
        image?: IFileContextWebhook;
        voice?: IFileContextWebhook;
        text?: ITextContextWebhook;
        sticker?: IStickerContext;
        location?: ILocationContextWebhook;
        system?: ITextContextWebhook;
        video?: IFileContextWebhook;
        context?: IReplyContext;
        unixtime: number,
        timestamp: firestore.Timestamp,
        type: TAvailablePostMessageContext,
        direction: EMessageDirection;

        interactive?: IInteractiveContextSendMessage | IInteractiveInboundContext;

        referral?: IReferralMessageContext;

    },
    statuses: TStatuses;
    error?: {
        "code": number,
        "href": string,
        "title": string
    };
    session_id?: string | null;
    bindContextAndDocuments?: {
        path: string | firestore.DocumentReference;
        context: string;
    }[];
    bindDocuments?: (string | firestore.DocumentReference)[];

}

type EMessageDirection = "IN" | "OUT";
