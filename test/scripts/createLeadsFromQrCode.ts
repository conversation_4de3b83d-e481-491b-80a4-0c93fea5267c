import createLeadsFromInboundMessage from "../../services/ideal_backend/createLeadsFromInboundMessage";

const createLeadsFromQrCode = async (
  params: {
    phoneNumber: string;
    messageContent: string;
    name: string;
  }
) => {
  try {
    await createLeadsFromInboundMessage({
      messageContent: params.messageContent,
      phoneNumber: params.phoneNumber,
      name: params.phoneNumber,
    })
  } catch (error) {
    console.log(error)
  }

}

const phoneNumber = "6289663600643"
const messageContent = "Halo saya mas bio,\n" +
  "Saya tertarik dengan produk VF 5 VF 5 4X2 WITH BATTERY dengan warna neptunegrey.\n" +
  "Saya telah bertemu dengan agen JSY.FL2AMARTAMOBIL.RAFLI KUSUMA PANGESTU, dengan referal TFC#IQLD2B#REF."
const name = "Bio J"

createLeadsFromQrCode({
  phoneNumber,
  messageContent,
  name,
});
