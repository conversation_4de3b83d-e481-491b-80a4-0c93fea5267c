import {myFirestore} from "../../services/google/firebaseAdmin";
import {
    IAdminSessionLog,
    IAdminSessionLogAnalyticData,
    ISessionLog,
    ISessionLogDataClient
} from "../../types/firestore/session_log_types";
import {Data} from "../../types/webhook/webWhatsappJsOnMessageWebhookTypes";
import {firestore} from "firebase-admin";
import moment from "moment";

const firestoreQueryTest = async () => {
    const collection = myFirestore.collection('admin_logs');
    const startAt = moment("2024-03-08", "YYYY-MM-DD")
        .startOf("date");
    const endAt = moment("2024-03-08 23:59", "YYYY-MM-DD HH:mm");

    console.log("Start at", startAt.format("YYYY-MM-DD HH:mm"))
    console.log("End at", endAt.format("YYYY-MM-DD HH:mm"))

    const getDocs = await collection
        .where("signed_in_at", ">", startAt.toDate())
        .where("signed_in_at", "<", endAt.toDate())
        // .where('auto_end_session_at', '<', endAt.toDate())
        // .where('sent_to_bigquery_at', '==', null)
        // .limit(500)
        .get();


    console.log(getDocs.size)


    getDocs.forEach(result => {
        const data = result.data() as IAdminSessionLog;
        // console.log(moment(data.signed_in_at.toDate()).format("YYYY-MM-DD HH:mm"), )

    })

}

firestoreQueryTest();
