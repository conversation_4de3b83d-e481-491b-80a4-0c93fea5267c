import {myFirestore} from "../../services/google/firebaseAdmin";

async function deleteClient() {
    const phoneNumber: string = "6285763705624";

    const clientCollection = myFirestore.collection("clients");
    const chatRoomCollection = myFirestore
        .collection("projects")
        .doc("v8GGopG1v89nd46aajEX")
        .collection("chat_rooms")

    const findClient = await clientCollection
        .where("profile.phone_number", "==", phoneNumber)
        .get();

    const batch = myFirestore.batch();
    if (!findClient.empty) {
        findClient.forEach(result => {
            batch.delete(result.ref);
        })
    }

    const getChatRoom = await chatRoomCollection
        .where("contacts", "array-contains", phoneNumber)
        .get();

    console.log(getChatRoom.size)

    if(!getChatRoom.empty) {
        getChatRoom.forEach(result => {
            batch.delete(result.ref);
        })
    }

    await batch.commit();
}

deleteClient();
