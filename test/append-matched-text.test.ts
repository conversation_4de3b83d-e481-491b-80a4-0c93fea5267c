import {describe, it} from "mocha";
import {URL} from "url";

describe("Append Search Text", () => {
    it('should append link', function () {
        let dummyText = "test dummy link https://other-link-amartahonda.com/baru/bandung/H1B02N42S1AT asdasd";

        const regex = /https?:\/\/(www\.)?amartahonda\.com\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)/gm
        const matches = dummyText.match(regex);

        console.log(matches)

        matches?.forEach(string => {
            const url = new URL(string);
            url.searchParams.set("trid", "123123123");
            dummyText = dummyText.replace(string, `${url.href}`)
        })

        console.log(dummyText)
    });
})