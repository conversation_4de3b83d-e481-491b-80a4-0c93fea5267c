import {myFirestore} from "../services/google/firebaseAdmin";
import {rejects} from "assert";
import axios from "axios";

let base = axios.create({
    baseURL: "http://localhost:8001/webhook/",
});


// base = axios.create({
//     baseURL: "https://asia-southeast2-ideal-trimitra.cloudfunctions.net/kata-ai-webhooks/webhook",
// });

describe('Get Message', () => {
    it('should return message', function (done) {
        new Promise(async resolve => {
            const messageDoc = myFirestore.doc('/log_webhook_qiscus_collection/not_developer/new_webhook/wamid.HBgNNjI4OTYwMTgyNTcyMxUCABIYIDEyMTFEMTBBMUU5N0ZEMjMxRTRBNzVBMkI0NUQzOTBCAA==')
            const getMessage = await messageDoc.get();
            resolve(getMessage.data());
        }).then(value => {
            console.log(JSON.stringify(value))
            done()
        })
    });


    it('should resend message', function (done) {
        new Promise(async (resolve, reject) => {
            const messageDoc = myFirestore.doc('/log_webhook_qiscus_collection/not_developer/new_webhook/ABGHYoExAFAULwIQ2xeEvZpcWPO00dOEKWbPKg')
            const getMessage = await messageDoc.get();
            const data = getMessage.data();

            try {
                const send = await base.post('/new-qiscus', data);
                resolve(send);
            } catch (e: any) {
                console.log(e)
                reject(e);
            }

        }).then(value => {
            console.log(JSON.stringify(value))
            done()
        })
    }).timeout(10000);

    it('should return message 2', function (done) {
        new Promise(async resolve => {
            const messageDoc = myFirestore.collection('/log_webhook_qiscus_collection/developer/new_webhook')
            const getMessage = await messageDoc.get();

            getMessage.forEach(result => {
                const data = result.data();

                if (data.messages[0].id === "ABGHYoV2NwViTwIKPrAvHc5oeZOWzQ") {
                    resolve(data);
                }
            })

        }).then(value => {
            console.log(JSON.stringify(value))
            done()
        })
    });


    it('should return wrong label', function (done) {
        new Promise(async resolve => {
            const projectChatRooms = myFirestore.collection('/projects/v8GGopG1v89nd46aajEX/chat_rooms')
            const findWrongLabel = await projectChatRooms
                .where('label', '==', myFirestore.doc('projects/v8GGopG1v89nd46aajEX/chat_rooms/38118891/labels/18BIn8vK76qZOVlvWnFx'))
                .get();

            const batch = myFirestore.batch();

            findWrongLabel.forEach(result => {
                batch.update(result.ref, {
                    label: myFirestore.doc('projects/v8GGopG1v89nd46aajEX/labels/18BIn8vK76qZOVlvWnFx'),
                })
            })

            try {
                await batch.commit();
                resolve(findWrongLabel.size);
            } catch (e: any) {
                rejects(e);
            }

        }).then(value => {
            console.log(JSON.stringify(value))
            done()
        }).catch(reason => {
            console.log(reason)
        })
    });

})
