import {describe} from "mocha";
import {catalogueServices} from "../services/catalogueServices";
import collect from "collect.js";
import {autotrimitraServices} from "../services/autotrimitraServices";
import {IVariant} from "../types/services/autotrimitra_service_types";

describe('Parse Link From Amarta Honda', () => {
    it('Parse Area', function () {
        return new Promise(async resolve => {
            const string = "bla bla bla https://amartahonda.com/baru/bandung/H1B02N42S1AT bla bla bla";

            const regex = new RegExp(
                /((([A-Za-z]{3,9}:(?:\/\/)?)(?:[-;:&=\+\$,\w]+@)?[A-Za-z0-9.-]+|(?:www.|[-;:&=\+\$,\w]+@)[A-Za-z0-9.-]+)((?:\/[\+~%\/.\w-_]*)?\??(?:[-\+=&;%@.\w_]*)#?(?:[.\!\/\\w]*))?)/,
                "gi"
            );

            const exec = string.match(regex);

            if (!!exec && exec.length > 0) {
                const first = exec[0];
                const url = new URL(first);
                if (url.hostname === "amartahonda.com") {
                    const segments = url
                        .pathname
                        .split('/')
                        .filter(value => !!value);

                    const citySegment = segments[1];

                    const fetchCity = await catalogueServices.getAvailableArea()
                    const cityCollection = collect(fetchCity.data?.data ?? []);
                    const group = cityCollection.groupBy('city_group');
                    const keys = group.keys();

                    const indexOfTarget = keys.toArray().indexOf(citySegment.toUpperCase());

                    if (indexOfTarget) {
                        resolve({
                            city: citySegment.toUpperCase(),
                            exists: true,
                        })
                    } else {
                        resolve(false);
                    }
                }
            } else {
                resolve(false);
            }
        })
    });

    it('parse variant', function () {
        return new Promise(async resolve => {
            const string = "bla bla bla https://amartahonda.com/baru/bandung/H1B02N42S1AT bla bla bla";

            const regex = new RegExp(
                /((([A-Za-z]{3,9}:(?:\/\/)?)(?:[-;:&=\+\$,\w]+@)?[A-Za-z0-9.-]+|(?:www.|[-;:&=\+\$,\w]+@)[A-Za-z0-9.-]+)((?:\/[\+~%\/.\w-_]*)?\??(?:[-\+=&;%@.\w_]*)#?(?:[.\!\/\\w]*))?)/,
                "gi"
            );

            let result: { variant: IVariant; exists: boolean } | false = false;

            const findAll = string.match(regex);

            if (!!findAll && findAll.length > 0) {
                for (const urlString of findAll) {
                    const url = new URL(urlString);
                    if (url.hostname === "amartahonda.com") {
                        const segments = url
                            .pathname
                            .split('/')
                            .filter(value => !!value);

                        if (segments[0].toLowerCase() == "baru") {
                            let variantCode = segments[2];
                            variantCode = variantCode.slice(0, -1) + "/" + variantCode.slice(-1);

                            const getVehicle = await autotrimitraServices.getVehicleVariantModel({
                                code: variantCode,
                            });

                            if (getVehicle.data.data.length == 1) {
                                result = {
                                    variant: getVehicle.data.data[0],
                                    exists: true,
                                };
                            } else {
                                result = false;
                            }
                        }
                        break;
                    }
                }
            } else {
                result = false;
            }

            console.log(result)

        })
    });
})