import axios from "axios";
import moment from "moment";

const base = axios.create({
    baseURL: "http://localhost:8001/webhook/",
});

// const base = axios.create({
//     baseURL: "https://asia-southeast2-ideal-trimitra.cloudfunctions.net/kata-ai-webhooks/webhook",
// });

describe('Webhook Interactive Flow', () => {
    it('reply 1', function (done) {
        new Promise(async (resolve, reject) => {
            const body = {
                "channel_id": 856,
                "contacts": [
                    {
                        "wa_id": "6285763705624",
                        "profile": {
                            "name": "<PERSON>fan <PERSON>ogo"
                        }
                    }
                ],
                "app_code": "novi-r1k2gj0hgdkessf4",
                "messages": [
                    {
                        "id": "ABGHYoV2NwViTwIKPrAvHc5oeZOWzQ",
                        "context": {
                            "from": "628112341361",
                            "id": "gBGHYoV2NwViTwIJ98kstzoEMZyT"
                        },
                        "type": "interactive",
                        "interactive": {
                            "type": "list_reply",
                            "list_reply": {
                                "description": "",
                                "id": "MBAK",
                                "title": "Mbak"
                            }
                        },
                        "timestamp": moment().unix().toString(),
                        "from": "6285763705624"
                    }
                ]
            }
            try {
                const send = await base.post('/new-qiscus', body);
                resolve(send);
            } catch (e: any) {
                console.log(e)
                reject(e);
            }

        }).then(value => {
            console.log(value);
            done();
        }).catch(done);
    }).timeout(5000);


    it('reply 2', function (done) {
        new Promise(async (resolve, reject) => {
            const body = {
                "channel_id": 856,
                "contacts": [
                    {
                        "wa_id": "6285763705624",
                        "profile": {
                            "name": "Taufan Budi Prayogo"
                        }
                    }
                ],
                "app_code": "novi-r1k2gj0hgdkessf4",
                "messages": [
                    {
                        "id": "ABGHYoV2NwViTwIKPrBPS8IRktUt9Q",
                        "timestamp": moment().unix().toString(),
                        "from": "6285763705624",
                        "text": {
                            "body": "Mbak Dummy"
                        },
                        "type": "text"
                    }
                ]
            }
            try {
                const send = await base.post('/new-qiscus', body);
                resolve(send.data);
            } catch (e: any) {
                reject(e);
            }

        }).then(value => {
            console.log(value);
            done();
        }).catch(done);
    }).timeout(5000);


    it('reply 3 - Buy Vehicle', function (done) {
        new Promise(async (resolve, reject) => {
            const body = {
                "channel_id": 856,
                "app_code": "novi-r1k2gj0hgdkessf4",
                "messages": [
                    {
                        "context": {
                            "id": "gBGHYoV2NwViTwIJ4MiGNCXqJf8g",
                            "from": "628112341361"
                        },
                        "interactive": {
                            "button_reply": {
                                "id": "BUY",
                                "title": "Pembelian Kendaraan"
                            },
                            "type": "button_reply"
                        },
                        "id": "ABGHYoV2NwViTwIKPrC9BSWp9kdOAQ",
                        "timestamp": moment().unix().toString(),
                        "from": "6285763705624",
                        "type": "interactive"
                    }
                ],
                "contacts": [
                    {
                        "profile": {
                            "name": "Taufan Budi Prayogo"
                        },
                        "wa_id": "6285763705624"
                    }
                ]
            };
            try {
                const send = await base.post('/new-qiscus', body);
                resolve(send);
            } catch (e: any) {
                reject(e);
            }

        }).then(value => {
            console.log(value);
            done();
        }).catch(done);
    }).timeout(5000);


    it('reply 4 - For Myself', function (done) {
        new Promise(async (resolve, reject) => {
            const body = {
                "app_code": "novi-r1k2gj0hgdkessf4",
                "contacts": [
                    {
                        "wa_id": "6285763705624",
                        "profile": {
                            "name": "Taufan Budi Prayogo"
                        }
                    }
                ],
                "channel_id": 856,
                "messages": [
                    {
                        "id": "ABGHYoV2NwViTwIKPrBuefcxYtbhFA",
                        "context": {
                            "id": "gBGHYoV2NwViTwIJJw2wgRk0MUCw",
                            "from": "628112341361"
                        },
                        "interactive": {
                            "button_reply": {
                                "title": "Ya",
                                "id": "YES"
                            },
                            "type": "button_reply"
                        },
                        "type": "interactive",
                        "timestamp": moment().unix().toString(),
                        "from": "6285763705624"
                    }
                ]
            };
            try {
                const send = await base.post('/new-qiscus', body);
                resolve(send);
            } catch (e: any) {
                reject(e);
            }

        }).then(value => {
            console.log(value);
            done();
        }).catch(done);
    }).timeout(5000);


    it('reply 5 - Purchase Scheme - Credit', function (done) {
        new Promise(async (resolve, reject) => {
            const body = {
                "messages": [
                    {
                        "type": "interactive",
                        "from": "6285763705624",
                        "timestamp": moment().unix().toString(),
                        "context": {
                            "id": "gBGHYoV2NwViTwIJpED8eJ0g-HY7",
                            "from": "628112341361"
                        },
                        "interactive": {
                            "button_reply": {
                                "title": "Kredit",
                                "id": "CREDIT"
                            },
                            "type": "button_reply"
                        },
                        "id": "ABGHYoV2NwViTwIKPrADrSqm4QIrtA"
                    }
                ],
                "contacts": [
                    {
                        "wa_id": "6285763705624",
                        "profile": {
                            "name": "Taufan Budi Prayogo"
                        }
                    }
                ],
                "app_code": "novi-r1k2gj0hgdkessf4",
                "channel_id": 856
            };
            try {
                const send = await base.post('/new-qiscus', body);
                resolve(send);
            } catch (e: any) {
                reject(e);
            }

        }).then(value => {
            console.log(value);
            done();
        }).catch(done);
    }).timeout(5000);


    it('reply 5 - Purchase Scheme - Cash', function (done) {
        new Promise(async (resolve, reject) => {
            const body = {
                "messages": [
                    {
                        "type": "interactive",
                        "from": "6285763705624",
                        "timestamp": moment().unix().toString(),
                        "context": {
                            "id": "gBGHYoV2NwViTwIJpED8eJ0g-HY7",
                            "from": "628112341361"
                        },
                        "interactive": {
                            "button_reply": {
                                "title": "Tunai",
                                "id": "CASH"
                            },
                            "type": "button_reply"
                        },
                        "id": "ABGHYoV2NwViTwIKPrADrSqm4QIrtA"
                    }
                ],
                "contacts": [
                    {
                        "wa_id": "6285763705624",
                        "profile": {
                            "name": "Taufan Budi Prayogo"
                        }
                    }
                ],
                "app_code": "novi-r1k2gj0hgdkessf4",
                "channel_id": 856
            };
            try {
                const send = await base.post('/new-qiscus', body);
                resolve(send);
            } catch (e: any) {
                reject(e);
            }

        }).then(value => {
            console.log(value);
            done();
        }).catch(done);
    }).timeout(5000);


    it('reply 6 - Had Loan Vehicle', function (done) {
        new Promise(async (resolve, reject) => {
            const body = {
                "contacts": [
                    {
                        "wa_id": "6285763705624",
                        "profile": {
                            "name": "Taufan Budi Prayogo"
                        }
                    }
                ],
                "app_code": "novi-r1k2gj0hgdkessf4",
                "channel_id": 856,
                "messages": [
                    {
                        "type": "interactive",
                        "id": "ABGHYoV2NwViTwIKPrAKRW4A6naFHg",
                        "interactive": {
                            "button_reply": {
                                "id": "YES",
                                "title": "Sudah"
                            },
                            "type": "button_reply"
                        },
                        "timestamp": moment().unix().toString(),
                        "from": "6285763705624",
                        "context": {
                            "from": "628112341361",
                            "id": "gBGHYoV2NwViTwIJ87Wo5HxFxf8V"
                        }
                    }
                ]
            };

            try {
                const send = await base.post('/new-qiscus', body);
                resolve(send);
            } catch (e: any) {
                reject(e);
            }

        }).then(value => {
            console.log(value);
            done();
        }).catch(done);
    }).timeout(5000);


    it('reply 7 - LAST - Select Vehicle Type', function (done) {
        new Promise(async (resolve, reject) => {
            const body = {
                "channel_id": 856,
                "app_code": "novi-r1k2gj0hgdkessf4",
                "contacts": [
                    {
                        "wa_id": "6285763705624",
                        "profile": {
                            "name": "Taufan Budi Prayogo"
                        }
                    }
                ],
                "messages": [
                    {
                        "type": "interactive",
                        "id": "ABGHYoV2NwViTwIKPrA4fmM4z6DVPg",
                        "timestamp": moment().unix().toString(),
                        "from": "6285763705624",
                        "interactive": {
                            "type": "list_reply",
                            "list_reply": {
                                "title": "Big Matic (150CC)",
                                "description": "",
                                "id": "BIG_MATIC"
                            }
                        },
                        "context": {
                            "id": "gBGHYoV2NwViTwIJ8mAk7SjFBZi6",
                            "from": "628112341361"
                        }
                    }
                ]
            };

            try {
                const send = await base.post('/new-qiscus', body);
                resolve(send.data);
            } catch (e: any) {
                reject(e);
            }

        }).then(value => {
            console.log(value);
            done();
        }).catch(done);
    }).timeout(5000);
})