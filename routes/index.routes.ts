import {Express} from "express";
import WebhookKataAi<PERSON>andler from "../handlers/kata-ai/webhookKataAiHandler";
import ClientLogHandler from "../handlers/ClientLogHandler";
import AdminLogHandler from "../handlers/AdminLogHandler";
import WebhookQiscusHandlerAutoReply from "../handlers/qiscuss/webhookQiscusHandlerAsAutoReply";
import QiscusWebhookHandler from "../handlers/qiscuss/qiscusWebhookHandler";
import WebWhatsappJsWebhook from "../handlers/webWhatsappJs/WebWhatsappJsWebhook";
import WebWhatsappJsWebhookAck from "../handlers/webWhatsappJs/WebWhatsappJsWebhookAck";
import WebWhatsappJsWebhookSendMessage from "../handlers/webWhatsappJs/WebWhatsappJsWebhookSendMessage";
import MetaWebhookNotificationHandler from "../handlers/meta/MetaWebhookNotificationHandler";
import MetaWebhookVerificationHandler from "../handlers/meta/MetaWebhookVerificationHandler";
import MetaWebhookTemplateHandler from "../handlers/meta/MetaWebhookTemplateHandler";
import MetaWebhookVerificationHandlerV2 from "../handlers/meta/v2/MetaWebhookVerificationHandler.v2";
import MetaWebhookNotificationHandlerV2 from "../handlers/meta/v2/MetaWebhookNotificationHandler.v2";
import TriforceInboundMessageHandler from "../handlers/triforce/triforceInboundMessageHandler";

export default function indexRoutes(app: Express) {
  app.get("/", (req, res) => {
    const d = new Date();

    res.send({
      messages: d.toLocaleTimeString(),
    });
  });

  app.post(
    "/broker-handler",
    WebhookKataAiHandler.middlewares,
    WebhookKataAiHandler.handler,
    WebhookKataAiHandler.handler2
  );

  app.post(
    "/webhook/new-qiscus",
    QiscusWebhookHandler.middlewares,
    QiscusWebhookHandler.handlerInboundMessage,
    QiscusWebhookHandler.handlerMessageNotification
  );

  app.post("/webhook/meta",
    MetaWebhookNotificationHandler.middlewares,
    MetaWebhookNotificationHandler.handler,
  );

  app.get("/webhook/meta",
    MetaWebhookVerificationHandler.middlewares,
    MetaWebhookVerificationHandler.handler,
  );

  app.get("/webhook/meta/:projectId",
    MetaWebhookVerificationHandlerV2.middlewares,
    MetaWebhookVerificationHandlerV2.handler as any,
  );

  app.post("/webhook/meta/:projectId",
    MetaWebhookNotificationHandlerV2.middlewares,
    MetaWebhookNotificationHandlerV2.handler,
  );

  app.post("/webhook/template/meta",
    MetaWebhookTemplateHandler.middlewares,
    MetaWebhookTemplateHandler.handler,
  );

  app.post(
    "/webhook/qiscuss-autoreply",
    WebhookQiscusHandlerAutoReply.middlewares,
    WebhookQiscusHandlerAutoReply.handler
  );

  app.post(
    "/webhook/web-whatsapp-send-message",
    WebWhatsappJsWebhookSendMessage.middlewares,
    WebWhatsappJsWebhookSendMessage.handler
  );

  app.post(
    "/webhook/web-whatsapp",
    WebWhatsappJsWebhook.middlewares,
    WebWhatsappJsWebhook.handler
  );

  app.post(
    "/webhook/web-whatsapp-ack",
    WebWhatsappJsWebhookAck.middlewares,
    WebWhatsappJsWebhookAck.handler
  );

  app.post(
    "/webhook/triforce",
    TriforceInboundMessageHandler.middlewares,
    TriforceInboundMessageHandler.handler
  );

  app.post(
    "/analytic/process",
    ClientLogHandler.middlewares,
    ClientLogHandler.handler
  );
  app.post(
    "/analytic-admin/process",
    AdminLogHandler.middlewares,
    AdminLogHandler.handler
  );

}
